import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  generateDeviceIdWithTimestamp,
  generateDeviceIdWithPrefix,
  isValidDeviceId,
  extractTimestampFromDeviceId,
  getDeviceCreationDate
} from '../deviceHelper.js';

describe('deviceHelper', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('generateDeviceIdWithTimestamp', () => {
    it('should generate device ID with SEA prefix and timestamp', () => {
      const deviceId = generateDeviceIdWithTimestamp();
      
      expect(deviceId).toMatch(/^SEA-\d+$/);
      expect(deviceId.startsWith('SEA-')).toBe(true);
      
      // Extract timestamp and verify it's recent
      const timestamp = parseInt(deviceId.split('-')[1], 10);
      const now = new Date().getTime();
      expect(timestamp).toBeGreaterThan(now - 1000); // Within last second
      expect(timestamp).toBeLessThanOrEqual(now);
    });

    it('should generate unique device IDs', () => {
      const deviceId1 = generateDeviceIdWithTimestamp();
      const deviceId2 = generateDeviceIdWithTimestamp();
      
      expect(deviceId1).not.toBe(deviceId2);
    });

    it('should generate device ID with current timestamp', () => {
      const beforeTime = new Date().getTime();
      const deviceId = generateDeviceIdWithTimestamp();
      const afterTime = new Date().getTime();
      
      const timestamp = parseInt(deviceId.split('-')[1], 10);
      expect(timestamp).toBeGreaterThanOrEqual(beforeTime);
      expect(timestamp).toBeLessThanOrEqual(afterTime);
    });
  });

  describe('generateDeviceIdWithPrefix', () => {
    it('should generate device ID with custom prefix', () => {
      const customPrefix = 'TEST';
      const deviceId = generateDeviceIdWithPrefix(customPrefix);
      
      expect(deviceId).toMatch(/^TEST-\d+$/);
      expect(deviceId.startsWith('TEST-')).toBe(true);
    });

    it('should work with different prefixes', () => {
      const prefixes = ['ABC', 'XYZ', 'DEV', 'PROD'];
      
      prefixes.forEach(prefix => {
        const deviceId = generateDeviceIdWithPrefix(prefix);
        expect(deviceId.startsWith(`${prefix}-`)).toBe(true);
      });
    });

    it('should handle empty prefix', () => {
      const deviceId = generateDeviceIdWithPrefix('');
      expect(deviceId).toMatch(/^-\d+$/);
    });
  });

  describe('isValidDeviceId', () => {
    it('should validate correct device ID format', () => {
      const validIds = [
        'SEA-1704067200000',
        'TEST-1234567890',
        'ABC-999999999999',
        'X-1'
      ];
      
      validIds.forEach(id => {
        expect(isValidDeviceId(id)).toBe(true);
      });
    });

    it('should reject invalid device ID formats', () => {
      const invalidIds = [
        'SEA1704067200000', // Missing dash
        'SEA-', // Missing timestamp
        '-1704067200000', // Missing prefix
        'sea-1704067200000', // Lowercase prefix
        'SEA-abc', // Non-numeric timestamp
        'SEA-123-456', // Multiple dashes
        '', // Empty string
        null, // Null
        undefined, // Undefined
        123, // Number
        {}, // Object
        [] // Array
      ];
      
      invalidIds.forEach(id => {
        expect(isValidDeviceId(id)).toBe(false);
      });
    });

    it('should handle edge cases', () => {
      expect(isValidDeviceId('A-0')).toBe(true);
      expect(isValidDeviceId('VERYLONGPREFIX-123456789')).toBe(true);
    });
  });

  describe('extractTimestampFromDeviceId', () => {
    it('should extract timestamp from valid device ID', () => {
      const timestamp = 1704067200000;
      const deviceId = `SEA-${timestamp}`;
      
      expect(extractTimestampFromDeviceId(deviceId)).toBe(timestamp);
    });

    it('should return null for invalid device IDs', () => {
      const invalidIds = [
        'SEA-abc',
        'SEA-',
        'invalid',
        '',
        null,
        undefined
      ];
      
      invalidIds.forEach(id => {
        expect(extractTimestampFromDeviceId(id)).toBeNull();
      });
    });

    it('should handle different prefixes', () => {
      const timestamp = 1704067200000;
      const deviceIds = [
        `TEST-${timestamp}`,
        `ABC-${timestamp}`,
        `X-${timestamp}`
      ];
      
      deviceIds.forEach(id => {
        expect(extractTimestampFromDeviceId(id)).toBe(timestamp);
      });
    });

    it('should handle edge case timestamps', () => {
      const timestamps = [0, 1, 999999999999];
      
      timestamps.forEach(timestamp => {
        const deviceId = `SEA-${timestamp}`;
        expect(extractTimestampFromDeviceId(deviceId)).toBe(timestamp);
      });
    });
  });

  describe('getDeviceCreationDate', () => {
    it('should return Date object for valid device ID', () => {
      const timestamp = 1704067200000;
      const deviceId = `SEA-${timestamp}`;
      const expectedDate = new Date(timestamp);
      
      const result = getDeviceCreationDate(deviceId);
      expect(result).toBeInstanceOf(Date);
      expect(result.getTime()).toBe(expectedDate.getTime());
    });

    it('should return null for invalid device IDs', () => {
      const invalidIds = [
        'SEA-abc',
        'invalid',
        '',
        null,
        undefined
      ];
      
      invalidIds.forEach(id => {
        expect(getDeviceCreationDate(id)).toBeNull();
      });
    });

    it('should handle different timestamps correctly', () => {
      const testCases = [
        { timestamp: 0, expected: new Date(0) },
        { timestamp: 1704067200000, expected: new Date(1704067200000) },
        { timestamp: Date.now(), expected: new Date(Date.now()) }
      ];
      
      testCases.forEach(({ timestamp, expected }) => {
        const deviceId = `SEA-${timestamp}`;
        const result = getDeviceCreationDate(deviceId);
        expect(result.getTime()).toBe(expected.getTime());
      });
    });
  });

  describe('integration tests', () => {
    it('should work end-to-end with generated device ID', () => {
      const deviceId = generateDeviceIdWithTimestamp();
      
      // Should be valid
      expect(isValidDeviceId(deviceId)).toBe(true);
      
      // Should extract timestamp
      const timestamp = extractTimestampFromDeviceId(deviceId);
      expect(timestamp).toBeGreaterThan(0);
      
      // Should get creation date
      const creationDate = getDeviceCreationDate(deviceId);
      expect(creationDate).toBeInstanceOf(Date);
      expect(creationDate.getTime()).toBe(timestamp);
    });

    it('should maintain consistency across all functions', () => {
      const customPrefix = 'TEST';
      const deviceId = generateDeviceIdWithPrefix(customPrefix);
      
      expect(isValidDeviceId(deviceId)).toBe(true);
      
      const timestamp = extractTimestampFromDeviceId(deviceId);
      expect(timestamp).toBeGreaterThan(0);
      
      const creationDate = getDeviceCreationDate(deviceId);
      expect(creationDate.getTime()).toBe(timestamp);
    });
  });
});
