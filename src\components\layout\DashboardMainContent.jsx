import React from 'react';
import Card from '../ui/Card';
import '../../styles/DashboardMainContent.css';

const DashboardMainContent = ({
  activeTab,
  courses = [],
  isLoading = false,
  onCourseClick
}) => {
  const renderHomeContent = () => {
    if (isLoading) {
      return (
        <div className="loading-container">
          <div className="spinner"></div>
          <p>Loading courses...</p>
        </div>
      );
    }

    if (courses.length === 0) {
      return (
        <div className="no-courses">
          <p>No courses available at the moment.</p>
        </div>
      );
    }

    return (
      <div className="courses-grid">
        {courses.map((course) => (
          <Card
            key={course.id}
            title={course.title}
            description={course.description}
            price={course.price}
            originalPrice={course.originalPrice}
            duration={course.duration}
            image={course.image}
            alt={`${course.title} course thumbnail`}
            onCardClick={() => onCourseClick(course.id)}
            className="course-card"
          />
        ))}
      </div>
    );
  };



  const renderCategoriesContent = () => (
    <div className="categories-section">
      <h2 className="h2 text-primary">Categories</h2>
      <p className="body2 text-secondary">Categories content coming soon...</p>
    </div>
  );

  const renderCoursesContent = () => (
    <div className="my-courses-section">
      <h2 className="h2 text-primary">My Courses</h2>
      <p className="body2 text-secondary">Your enrolled courses will appear here...</p>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'home':
        return renderHomeContent();
      case 'categories':
        return renderCategoriesContent();
      case 'courses':
        return renderCoursesContent();
      default:
        return renderHomeContent();
    }
  };

  return (
    <div className="dashboard-main-content">
      <div className="content-container">
        {renderContent()}
      </div>
    </div>
  );
};

export default DashboardMainContent;
