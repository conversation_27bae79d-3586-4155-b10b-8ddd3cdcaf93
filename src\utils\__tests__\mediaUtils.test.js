import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  getPdfUrl,
  getAudioUrl,
  validateMediaUrl,
  getMediaUrlWithFallback,
  extractFilenameFromUrl,
  hasValidMediaExtension,
  getMediaTypeFromFilename,
  preloadMediaUrl
} from '../mediaUtils.js';

// Mock fetch for URL validation tests
global.fetch = vi.fn();

describe('mediaUtils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getPdfUrl', () => {
    it('should construct PDF URL with base URL', () => {
      const filename = 'document.pdf';
      const result = getPdfUrl(filename);
      
      expect(result).toBe('https://somayyaacademy.s3.ap-south-amazonaws.com/somayyaAcademyEdTech/Pdf/document.pdf');
    });

    it('should handle filenames with leading slashes', () => {
      const filename = '/folder/document.pdf';
      const result = getPdfUrl(filename);
      
      expect(result).toBe('https://somayyaacademy.s3.ap-south-amazonaws.com/somayyaAcademyEdTech/Pdf/folder/document.pdf');
    });

    it('should return absolute URLs unchanged', () => {
      const absoluteUrl = 'https://example.com/document.pdf';
      const result = getPdfUrl(absoluteUrl);
      
      expect(result).toBe(absoluteUrl);
    });

    it('should handle empty or invalid filenames', () => {
      expect(getPdfUrl('')).toBe('');
      expect(getPdfUrl(null)).toBe('');
      expect(getPdfUrl(undefined)).toBe('');
    });

    it('should handle protocol-relative URLs', () => {
      const protocolRelativeUrl = '//example.com/document.pdf';
      const result = getPdfUrl(protocolRelativeUrl);
      
      expect(result).toBe(protocolRelativeUrl);
    });
  });

  describe('getAudioUrl', () => {
    it('should construct Audio URL with base URL', () => {
      const filename = 'audio.mp3';
      const result = getAudioUrl(filename);
      
      expect(result).toBe('https://somayyaacademy.s3.ap-south-amazonaws.com/somayyaAcademyEdTech/Audio/audio.mp3');
    });

    it('should handle filenames with leading slashes', () => {
      const filename = '/folder/audio.mp3';
      const result = getAudioUrl(filename);
      
      expect(result).toBe('https://somayyaacademy.s3.ap-south-amazonaws.com/somayyaAcademyEdTech/Audio/folder/audio.mp3');
    });

    it('should return absolute URLs unchanged', () => {
      const absoluteUrl = 'https://example.com/audio.mp3';
      const result = getAudioUrl(absoluteUrl);
      
      expect(result).toBe(absoluteUrl);
    });

    it('should handle empty or invalid filenames', () => {
      expect(getAudioUrl('')).toBe('');
      expect(getAudioUrl(null)).toBe('');
      expect(getAudioUrl(undefined)).toBe('');
    });
  });

  describe('validateMediaUrl', () => {
    it('should return true for accessible URLs', async () => {
      fetch.mockResolvedValueOnce({ ok: true });
      
      const result = await validateMediaUrl('https://example.com/file.pdf');
      
      expect(result).toBe(true);
      expect(fetch).toHaveBeenCalledWith('https://example.com/file.pdf', { method: 'HEAD' });
    });

    it('should return false for inaccessible URLs', async () => {
      fetch.mockResolvedValueOnce({ ok: false });
      
      const result = await validateMediaUrl('https://example.com/nonexistent.pdf');
      
      expect(result).toBe(false);
    });

    it('should return false for network errors', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));
      
      const result = await validateMediaUrl('https://example.com/file.pdf');
      
      expect(result).toBe(false);
    });

    it('should return false for empty URLs', async () => {
      const result = await validateMediaUrl('');
      
      expect(result).toBe(false);
      expect(fetch).not.toHaveBeenCalled();
    });
  });

  describe('getMediaUrlWithFallback', () => {
    it('should return primary URL if accessible', async () => {
      fetch.mockResolvedValueOnce({ ok: true });
      
      const result = await getMediaUrlWithFallback('document.pdf', 'pdf', ['https://fallback.com/doc.pdf']);
      
      expect(result).toBe('https://somayyaacademy.s3.ap-south-amazonaws.com/somayyaAcademyEdTech/Pdf/document.pdf');
    });

    it('should return fallback URL if primary fails', async () => {
      fetch
        .mockResolvedValueOnce({ ok: false }) // Primary fails
        .mockResolvedValueOnce({ ok: true });  // Fallback succeeds
      
      const result = await getMediaUrlWithFallback('document.pdf', 'pdf', ['https://fallback.com/doc.pdf']);
      
      expect(result).toBe('https://fallback.com/doc.pdf');
    });

    it('should return empty string if all URLs fail', async () => {
      fetch.mockResolvedValue({ ok: false });
      
      const result = await getMediaUrlWithFallback('document.pdf', 'pdf', ['https://fallback.com/doc.pdf']);
      
      expect(result).toBe('');
    });
  });

  describe('extractFilenameFromUrl', () => {
    it('should extract filename from URL', () => {
      const url = 'https://example.com/folder/document.pdf';
      const result = extractFilenameFromUrl(url);
      
      expect(result).toBe('document.pdf');
    });

    it('should handle URLs without filename', () => {
      const url = 'https://example.com/folder/';
      const result = extractFilenameFromUrl(url);
      
      expect(result).toBe('');
    });

    it('should handle invalid URLs', () => {
      const result = extractFilenameFromUrl('not-a-url');
      
      expect(result).toBe('not-a-url');
    });

    it('should handle empty input', () => {
      expect(extractFilenameFromUrl('')).toBe('');
      expect(extractFilenameFromUrl(null)).toBe('');
    });
  });

  describe('hasValidMediaExtension', () => {
    it('should validate PDF extensions', () => {
      expect(hasValidMediaExtension('document.pdf', 'pdf')).toBe(true);
      expect(hasValidMediaExtension('document.PDF', 'pdf')).toBe(true);
      expect(hasValidMediaExtension('document.txt', 'pdf')).toBe(false);
    });

    it('should validate audio extensions', () => {
      expect(hasValidMediaExtension('audio.mp3', 'audio')).toBe(true);
      expect(hasValidMediaExtension('audio.wav', 'audio')).toBe(true);
      expect(hasValidMediaExtension('audio.ogg', 'audio')).toBe(true);
      expect(hasValidMediaExtension('audio.m4a', 'audio')).toBe(true);
      expect(hasValidMediaExtension('audio.txt', 'audio')).toBe(false);
    });

    it('should handle invalid inputs', () => {
      expect(hasValidMediaExtension('', 'pdf')).toBe(false);
      expect(hasValidMediaExtension(null, 'pdf')).toBe(false);
      expect(hasValidMediaExtension('file.pdf', 'invalid')).toBe(false);
    });
  });

  describe('getMediaTypeFromFilename', () => {
    it('should detect PDF files', () => {
      expect(getMediaTypeFromFilename('document.pdf')).toBe('pdf');
      expect(getMediaTypeFromFilename('document.PDF')).toBe('pdf');
    });

    it('should detect audio files', () => {
      expect(getMediaTypeFromFilename('audio.mp3')).toBe('audio');
      expect(getMediaTypeFromFilename('audio.wav')).toBe('audio');
      expect(getMediaTypeFromFilename('audio.ogg')).toBe('audio');
    });

    it('should return null for unknown types', () => {
      expect(getMediaTypeFromFilename('document.txt')).toBeNull();
      expect(getMediaTypeFromFilename('image.jpg')).toBeNull();
    });

    it('should handle invalid inputs', () => {
      expect(getMediaTypeFromFilename('')).toBeNull();
      expect(getMediaTypeFromFilename(null)).toBeNull();
    });
  });

  describe('preloadMediaUrl', () => {
    it('should preload PDF URLs', async () => {
      fetch.mockResolvedValueOnce({ ok: true });
      
      const result = await preloadMediaUrl('https://example.com/doc.pdf', 'pdf');
      
      expect(result).toBe(true);
      expect(fetch).toHaveBeenCalledWith('https://example.com/doc.pdf', { method: 'HEAD' });
    });

    it('should handle preload failures', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));
      
      const result = await preloadMediaUrl('https://example.com/doc.pdf', 'pdf');
      
      expect(result).toBe(false);
    });

    it('should handle empty URLs', async () => {
      const result = await preloadMediaUrl('', 'pdf');
      
      expect(result).toBe(false);
      expect(fetch).not.toHaveBeenCalled();
    });

    it('should handle audio preloading', async () => {
      // Mock Audio constructor
      global.Audio = vi.fn().mockImplementation(() => ({
        addEventListener: vi.fn((event, callback) => {
          if (event === 'canplaythrough') {
            setTimeout(() => callback(), 0);
          }
        }),
        load: vi.fn(),
        src: ''
      }));

      const result = await preloadMediaUrl('https://example.com/audio.mp3', 'audio');
      
      expect(result).toBe(true);
    });
  });

  describe('URL construction edge cases', () => {
    it('should handle multiple leading slashes', () => {
      const result = getPdfUrl('///folder/document.pdf');
      expect(result).toBe('https://somayyaacademy.s3.ap-south-amazonaws.com/somayyaAcademyEdTech/Pdf/folder/document.pdf');
    });

    it('should handle whitespace in filenames', () => {
      const result = getPdfUrl('  document.pdf  ');
      expect(result).toBe('https://somayyaacademy.s3.ap-south-amazonaws.com/somayyaAcademyEdTech/Pdf/document.pdf');
    });

    it('should preserve query parameters and fragments', () => {
      const filename = 'document.pdf?version=1#page=2';
      const result = getPdfUrl(filename);
      expect(result).toBe('https://somayyaacademy.s3.ap-south-amazonaws.com/somayyaAcademyEdTech/Pdf/document.pdf?version=1#page=2');
    });
  });
});
