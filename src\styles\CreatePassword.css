/* Outer wrapper */
.create-password-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 80px);
  padding: 2rem 1rem;
}

/* Layout container for card and sidebar */
.create-password-layout {
  display: flex;
  align-items: center;
  gap: 2rem;
  max-width: 800px;
  width: 100%;
}

/* Main card styling */
.create-password-box {
  flex: 1;
  max-width: 450px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.create-password-title {
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
}

/* Form styling */
.create-password-box .form-group {
  margin-bottom: 1.5rem;
}

.create-password-box .form-group:last-of-type {
  margin-bottom: 2rem;
}

.create-password-box .password-wrapper {
  position: relative;
}

.create-password-box .password-wrapper input {
  width: 100%;
  padding: 0.875rem 3rem 0.875rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-color);
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.create-password-box .password-wrapper input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.create-password-box .toggle-password {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--secondary-text-color);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.create-password-box .toggle-password:hover {
  color: var(--text-color);
}

/* Button styling */
.create-password-box .login-button {
  width: 100%;
  padding: 0.875rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  background: var(--text-color);
  border: none;
  color: var(--background-color);
  cursor: pointer;
  transition: all 0.3s ease;
}

.create-password-box .login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.create-password-box .login-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Password requirements sidebar - Outside the card */
.password-requirements-sidebar {
  flex-shrink: 0;
  width: 280px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.6);
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.requirements-header {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--secondary-text-color);
  margin-bottom: 1rem;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.requirements-list li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.4;
  color: var(--secondary-text-color);
}

.requirement-icon {
  font-size: 1rem;
  min-width: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.requirement-text {
  flex: 1;
}

.requirements-list li.valid {
  color: #22c55e;
}

.requirements-list li.invalid {
  color: var(--secondary-text-color);
}

/* Responsive design */
@media (max-width: 768px) {
  .create-password-wrapper {
    padding: 1rem;
    min-height: calc(100vh - 60px);
  }

  .create-password-layout {
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
  }

  .create-password-box {
    max-width: 100%;
    padding: 1.5rem;
    border-radius: 16px;
  }

  .create-password-title {
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
  }

  .password-requirements-sidebar {
    width: 100%;
    max-width: 400px;
    padding-top: 0;
    order: -1; /* Show requirements above form on mobile */
  }

  .requirements-header {
    text-align: center;
    font-size: 1rem;
  }

  .requirements-list {
    gap: 0.375rem;
  }

  .requirements-list li {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .create-password-wrapper {
    padding: 0.5rem;
  }

  .create-password-box {
    padding: 1rem;
  }

  .create-password-layout {
    gap: 1rem;
  }
}
