import React, { useEffect } from 'react';
import '../../styles/Modal.css';

/**
 * NavigationBlockModal - Custom modal for blocking navigation when profile is incomplete
 * Matches the project's design system and provides consistent UX
 */
const NavigationBlockModal = ({
  isOpen,
  onStay,
  onLeave,
  onLogout,
  title = "Unsaved Changes",
  message = "Your changes haven't been saved. Are you sure you want to leave this page?",
  showLogoutOption = false
}) => {
  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add('modal-open');

      // Focus trap - focus the modal when it opens
      const modalElement = document.querySelector('.modal-content');
      if (modalElement) {
        modalElement.focus();
      }
    } else {
      document.body.classList.remove('modal-open');
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('modal-open');
    };
  }, [isOpen]);

  // Handle keyboard events
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event) => {
      switch (event.key) {
        case 'Escape':
          onStay();
          break;
        case 'Enter':
          // Default action is to stay (safer option)
          onStay();
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onStay]);

  if (!isOpen) {
    return null;
  }

  // Handle overlay click (clicking outside modal)
  const handleOverlayClick = (event) => {
    if (event.target === event.currentTarget) {
      onStay(); // Default to staying (safer option)
    }
  };

  return (
    <div
      className="modal-overlay"
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
    >
      <div
        className="modal-content"
        tabIndex={-1}
      >
        <h2 id="modal-title">{title}</h2>
        <div id="modal-description">{message}</div>
        <div className="modal-actions">
          <button
            onClick={onStay}
            className="cancel-button primary-button"
            autoFocus
            aria-label="Stay on this page and continue editing"
          >
            Stay on Page
          </button>
          <button
            onClick={onLeave}
            className="confirm-button"
            aria-label="Leave this page without saving changes"
          >
            Leave Without Saving
          </button>
          {showLogoutOption && onLogout && (
            <button
              onClick={onLogout}
              className="confirm-button"
              aria-label="Log out and return to login page"
            >
              Log Out
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default NavigationBlockModal;
