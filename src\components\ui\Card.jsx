import React from 'react';
import { HiOutlineClock } from 'react-icons/hi';

import '../../styles/Card.css';

const Card = ({
  title = "Structural Design - 1",
  price = "₹XXXX",
  duration = "4 months",
  image,
  alt,
  onCardClick,
  className = "",
  showClockIcon = true
}) => {
  return (
    <div
      className={`card ${onCardClick ? 'card-clickable' : ''} ${className}`}
      onClick={onCardClick}
    >
      <div className="card-content">
        {/* Image Section - Always render container, show placeholder if no image */}
        <div className={`card-image-container ${!image ? 'card-image-placeholder' : ''}`}>
          {image ? (
            <img
              src={image}
              alt={alt || title}
              className="card-image"
            />
          ) : null}
        </div>

        {/* Title */}
        <h3 className="h6 card-title">
          {title}
        </h3>

        {/* Price and Duration Row */}
        <div className="card-footer">
          {/* Price */}
          <span className="body4 card-price">
            Price {price}
          </span>

          {/* Duration and Clock Icon */}
          <div className="card-duration-section">
            <span className="body3 card-duration">
              {duration}
            </span>
            {showClockIcon && (
              <div className="card-clock-icon">
                <HiOutlineClock size={16} />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Card;