import React, { useEffect, useRef } from "react";
import { Outlet } from "react-router-dom";
import "./styles/App.css";
import {
  LoginPage,
  SignupPage,
  OTPVerificationPage,
  CreatePasswordPage,
  ReadingPage,
  TermsPage,
  PrivacyPage,
  UserDetails,
  Dashboard,
  Profile,
  TopicsPage
} from "./pages";
import { ErrorBoundary } from "./components/common";
import { useAuthStore } from "./stores";
import { useErrorHandler } from "./hooks";
import { ToastProvider } from "./contexts/ToastContext";

// Main App content component
export function AppContent() {
  const { addError } = useErrorHandler();

  // Initialize audio context on first user interaction
  useEffect(() => {
    const initializeAudioContext = () => {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      if (AudioContext) {
        const audioContext = new AudioContext();

        // Resume audio context if suspended
        if (audioContext.state === "suspended") {
          audioContext.resume();
        }
      }
    };

    // Add event listener for first user interaction
    const handleFirstInteraction = () => {
      initializeAudioContext();
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };

    document.addEventListener("click", handleFirstInteraction);
    document.addEventListener("touchstart", handleFirstInteraction);

    return () => {
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };
  }, []);

  return (
    <ToastProvider>
      <ErrorBoundary
        message={null} // Let ErrorBoundary use the actual error message
        onError={(error, errorInfo) => {
          console.error("App Error Boundary:", error, errorInfo);
          addError({
            id: Date.now(),
            message: error.message || "An error occurred. Please refresh the page.",
            type: "error",
          });
        }}
      >
        <Outlet />
      </ErrorBoundary>
    </ToastProvider>
  );
}

export default AppContent;
