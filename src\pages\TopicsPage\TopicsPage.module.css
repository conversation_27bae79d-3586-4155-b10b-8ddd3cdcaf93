.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.title {
  font-size: 2rem;
  color: #2c3e50;
  margin-bottom: 2rem;
  font-weight: 600;
}

.topicsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.topicCard {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.topicHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.topicHeader:hover {
  background-color: #e9ecef;
}

.topicHeader.expanded {
  background-color: #e9ecef;
  border-bottom: 1px solid #dee2e6;
}

.topicTitle {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: #343a40;
}

.arrowIcon {
  font-size: 0.8rem;
  color: #6c757d;
  transition: transform 0.2s ease;
}

.topicHeader.expanded .arrowIcon {
  transform: rotate(180deg);
}

.resourcesContainer {
  background-color: #ffffff;
  padding: 1.5rem;
  border-top: 1px solid #e9ecef;
}

.loadingResources {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  padding: 1rem 0;
  justify-content: center;
}

.resourcesList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.resourceItem {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.resourceItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pdfContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.pdfContainer:hover {
  background-color: #f1f3f5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pdfIcon {
  font-size: 1.5rem;
  color: #e74c3c;
  flex-shrink: 0;
}

.resourceInfo {
  flex: 1;
  min-width: 0;
}

.resourceName {
  display: block;
  font-weight: 500;
  color: #212529;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.resourceMeta {
  display: block;
  font-size: 0.8rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

.viewButton {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.4rem 0.8rem;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;
}

.viewButton:hover {
  background-color: #2980b9;
  transform: translateY(-1px);
}

.viewButton:active {
  transform: translateY(0);
}

.audioList {
  margin-top: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px dashed #dee2e6;
}

.audioItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding: 0.5rem 0.75rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.audioItem:hover {
  background-color: #f1f3f5;
}

.audioInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.audioIcon {
  font-size: 1.25rem;
  color: #9b59b6;
  flex-shrink: 0;
}

.audioDetails {
  flex: 1;
  min-width: 0;
}

.audioName {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: #2c3e50;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.audioMeta {
  display: block;
  font-size: 0.75rem;
  color: #7f8c8d;
  margin-top: 0.15rem;
}

.playButton {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #2ecc71;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.playButton:hover {
  background-color: #27ae60;
  transform: scale(1.1);
}

.playButton.playing {
  background-color: #e74c3c;
  width: 32px;
  border-radius: 4px;
}

.playButton.playing:hover {
  background-color: #c0392b;
}

.noResources {
  color: #6c757d;
  text-align: center;
  padding: 1.5rem 0;
  font-style: italic;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: 1rem;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: 1rem;
  text-align: center;
  padding: 2rem;
}

.errorText {
  color: #dc3545;
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.retryButton {
  padding: 0.5rem 1.5rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s ease;
}

.retryButton:hover {
  background-color: #0056b3;
}
