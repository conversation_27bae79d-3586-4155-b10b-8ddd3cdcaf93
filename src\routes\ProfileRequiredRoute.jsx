import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { validateAuthState, validateUserDetailsAccess, clearAuthStorage } from '../utils/authValidation';

/**
 * ProfileRequiredRoute component - Only accessible when authenticated AND profile is complete
 * Provides comprehensive authentication validation including token validation
 * Redirects to login if authentication is invalid
 * Redirects to user-details if authenticated but profile is incomplete
 */
const ProfileRequiredRoute = ({ children }) => {
  const { isAuthenticated, currentUser } = useAuthStore();
  const location = useLocation();

  // Comprehensive authentication validation
  const authState = { isAuthenticated, currentUser };
  const authValidation = validateAuthState(authState);

  console.log('🔍 ProfileRequiredRoute validation:', {
    path: location.pathname,
    isAuthenticated,
    hasUser: !!currentUser,
    hasToken: !!currentUser?.token,
    authValidation
  });

  // If authentication is invalid, clear storage and redirect to login
  if (!authValidation.isValid) {
    console.log('❌ Authentication invalid, clearing storage and redirecting to login:', authValidation.reason);

    // Clear any stale authentication data
    clearAuthStorage(false); // Don't clear persisted store data here to avoid conflicts

    return <Navigate to="/login" replace />;
  }

  // Check if user should have access to user-details page specifically
  if (location.pathname === '/user-details') {
    const userDetailsAccess = validateUserDetailsAccess(authState);

    if (userDetailsAccess.shouldRedirectToLogin) {
      console.log('❌ UserDetails access denied, redirecting to login:', userDetailsAccess.reason);
      clearAuthStorage(false);
      return <Navigate to="/login" replace />;
    }

    if (userDetailsAccess.shouldRedirectToDashboard) {
      console.log('✅ Profile complete, redirecting to dashboard:', userDetailsAccess.reason);
      return <Navigate to="/dashboard" replace />;
    }

    if (userDetailsAccess.hasAccess) {
      console.log('✅ UserDetails access granted:', userDetailsAccess.reason);
      return children;
    }
  }

  // For other protected routes, check if profile is complete
  const isProfileComplete = currentUser?.is_profile_set === true;

  if (!isProfileComplete) {
    console.log('⚠️ Profile incomplete, redirecting to user-details');
    return <Navigate to="/user-details" replace />;
  }

  console.log('✅ Access granted to protected route');
  return children;
};

export default ProfileRequiredRoute;
