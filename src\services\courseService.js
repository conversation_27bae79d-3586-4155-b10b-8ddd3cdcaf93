/**
 * Course Service - Handles course-related API calls
 */
import { apiClient, API_ENDPOINTS, withErrorHandling } from '../api/index.js';

class CourseService {
  constructor() {
    this.apiClient = apiClient;
  }

  /**
   * Fetch all courses
   * @returns {Promise} Service response with courses data
   */
  async getCourses() {
    return await withErrorHandling(
      async () => {
        const response = await this.apiClient.get(API_ENDPOINTS.content.courses);
        return response.data;
      },
      { operation: 'getCourses' }
    );
  }

  /**
   * Fetch a single course by ID
   * @param {string} courseId - The ID of the course to fetch
   * @returns {Promise} Service response with course data
   */
  async getCourseById(courseId) {
    return await withErrorHandling(
      async () => {
        const response = await this.apiClient.get(
          API_ENDPOINTS.content.courseById(courseId)
        );
        return response.data;
      },
      { operation: 'getCourseById', courseId }
    );
  }

  /**
   * Fetch course details with curriculum/subjects
   * @param {string} courseId - The ID of the course to fetch
   * @returns {Promise} Service response with detailed course data
   */
  async getCourseDetails(courseId) {
    return await withErrorHandling(
      async () => {
        const response = await this.apiClient.get(
          API_ENDPOINTS.content.courseById(courseId)
        );
        return response.data;
      },
      { operation: 'getCourseDetails', courseId }
    );
  }
}

// Create a singleton instance
export const courseService = new CourseService();

export default courseService;
