import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';

/**
 * PublicRoute component - Only accessible when NOT authenticated
 * Redirects to appropriate page based on authentication and profile completion status
 * Special handling for login page to allow logout even with incomplete profile
 */
const PublicRoute = ({ children }) => {
  const { isAuthenticated, currentUser } = useAuthStore();
  const location = useLocation();

  if (!isAuthenticated) {
    return children;
  }

  // Allow access to login page even if authenticated (for logout functionality)
  if (location.pathname === '/login') {
    return children;
  }

  // User is authenticated, check profile completion status
  const isProfileComplete = currentUser?.is_profile_set === true;

  if (isProfileComplete) {
    return <Navigate to="/dashboard" replace />;
  } else {
    return <Navigate to="/user-details" replace />;
  }
};

export default PublicRoute;
