import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import CourseDetailPage from '../index';
import { courseService } from '../../../services/courseService';
import { topicService } from '../../../services/topicService';

// Mock the services
vi.mock('../../../services/courseService');
vi.mock('../../../services/topicService');
vi.mock('react-toastify', () => ({
  toast: {
    error: vi.fn(),
  },
}));

// Mock useParams to return a courseId
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ courseId: 'test-course-id' }),
    useNavigate: () => vi.fn(),
  };
});

const renderWithRouter = (component) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('CourseDetailPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render loading state initially', () => {
    // Mock services to return pending promises
    courseService.getCourseDetails.mockReturnValue(new Promise(() => {}));
    topicService.getTopicsByCourseId.mockReturnValue(new Promise(() => {}));

    renderWithRouter(<CourseDetailPage />);

    expect(screen.getByText('Loading course details...')).toBeInTheDocument();
  });

  it('should render course details when data is loaded', async () => {
    const mockCourse = {
      id: 'test-course-id',
      title: 'Engineering Mechanics',
      description: 'Test course description',
    };

    const mockTopics = [
      {
        id: 'topic-1',
        title: 'Resultant of Force System',
        subject: 'Statics',
      },
      {
        id: 'topic-2',
        title: 'Centre of gravity',
        subject: 'Statics',
      },
    ];

    // Mock the API response structure - course details come from /courses/getAll?courseId=X
    courseService.getCourseDetails.mockResolvedValue({
      data: mockCourse,
    });

    topicService.getTopicsByCourseId.mockResolvedValue({
      data: { data: mockTopics },
    });

    renderWithRouter(<CourseDetailPage />);

    await waitFor(() => {
      expect(screen.getByText('Engineering Mechanics')).toBeInTheDocument();
    });

    expect(screen.getByText('BACK')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Curriculum')).toBeInTheDocument();
  });

  it('should show error state when course is not found', async () => {
    courseService.getCourseDetails.mockResolvedValue({
      data: null,
    });

    topicService.getTopicsByCourseId.mockResolvedValue({
      data: { data: [] },
    });

    renderWithRouter(<CourseDetailPage />);

    await waitFor(() => {
      expect(screen.getByText('Course not found')).toBeInTheDocument();
    });

    expect(screen.getByText('BACK')).toBeInTheDocument();
  });

  it('should handle service errors gracefully', async () => {
    courseService.getCourseDetails.mockRejectedValue(new Error('API Error'));
    topicService.getTopicsByCourseId.mockRejectedValue(new Error('API Error'));

    renderWithRouter(<CourseDetailPage />);

    await waitFor(() => {
      expect(screen.queryByText('Loading course details...')).not.toBeInTheDocument();
    });

    // Should still render the basic structure even with errors
    expect(screen.getByText('Course not found')).toBeInTheDocument();
  });
});
