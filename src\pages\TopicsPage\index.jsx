import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { apiClient } from '../../api';
import { LoadingSpinner } from '../../components';
import { getAudioUrl, getPdfUrl } from '../../utils/mediaUtils';
import styles from './TopicsPage.module.css';

// Utility function to format file size
const formatFileSize = (bytes) => {
  if (!bytes) return '';
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

const TopicsPage = () => {
  const { courseId } = useParams();
  const navigate = useNavigate();
  const [topics, setTopics] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [expandedTopic, setExpandedTopic] = useState(null);
  const [resources, setResources] = useState({});
  const [loadingResources, setLoadingResources] = useState({});

  // Initialize API client and fetch topics for the course
  useEffect(() => {
    let isMounted = true;

    const initializeAndFetch = async () => {
      console.group('TopicsPage - Fetching Topics');
      console.log('Course ID:', courseId);

      if (!isMounted) {
        console.log('Component unmounted, aborting');
        return;
      }

      try {
        setLoading(true);

        // Debug API client state
        console.log('API Client State:', {
          isInitialized: apiClient.isInitialized,
          axiosInstance: !!apiClient.axiosInstance
        });

        // Initialize the API client
        console.log('Initializing API client...');
        const client = await apiClient.initialize();
        console.log('API client initialized:', {
          isInitialized: apiClient.isInitialized,
          axiosInstance: !!client,
          baseURL: client?.defaults?.baseURL
        });

        // Make the API call to get topics
        const endpoint = `/topics/getAll?courseId=${courseId}`;
        const fullUrl = client?.defaults?.baseURL ?
          `${client.defaults.baseURL}${endpoint}` :
          endpoint;

        console.log('Making API call to:', fullUrl);

        const response = await client.get(endpoint);
        console.log('API response received:', {
          status: response.status,
          statusText: response.statusText,
          data: response.data ? 'Received data' : 'No data',
          dataLength: Array.isArray(response.data?.data) ? response.data.data.length : 'N/A'
        });

        if (isMounted) {
          if (response.data && Array.isArray(response.data.data)) {
            console.log(`Received ${response.data.data.length} topics`);
            setTopics(response.data.data);
          } else {
            console.warn('Unexpected API response format:', response.data);
            setError('Unexpected response format from server');
          }
        }
      } catch (err) {
        console.error('Error fetching topics:', err);

        const errorDetails = {
          message: err.message,
          code: err.code,
          status: err.response?.status,
          statusText: err.response?.statusText,
          responseData: err.response?.data
        };

        console.error('Error details:', errorDetails);

        if (isMounted) {
          const errorMsg = errorDetails.responseData?.message ||
                         errorDetails.message ||
                         'Failed to load topics. Please try again later.';
          setError(errorMsg);
        }
      } finally {
        if (isMounted) {
          console.groupEnd();
          setLoading(false);
        }
      }
    };

    if (courseId) {
      console.log('Starting initialization and fetch...');
      initializeAndFetch().catch(error => {
        console.error('Unhandled error in initializeAndFetch:', error);
      });
    } else {
      console.warn('No courseId provided');
      setError('No course ID provided');
      setLoading(false);
    }

    return () => {
      isMounted = false;
      console.log('Cleanup: Component unmounted or courseId changed');
    };
  }, [courseId]);

  // Fetch resources for a topic when expanded
  const fetchTopicResources = async (topicId) => {
    if (resources[topicId]) return; // Skip if already loaded

    console.group(`Fetching resources for topic ${topicId}`);

    try {
      setLoadingResources(prev => ({ ...prev, [topicId]: true }));

      // Get the initialized API client
      const client = await apiClient.initialize();

      // Make the API call to get resources with the correct endpoint format
      const endpoint = `/resources/topicId/${topicId}`;
      const fullUrl = client?.defaults?.baseURL ?
        `${client.defaults.baseURL}${endpoint}` :
        endpoint;

      console.log('Fetching resources from:', fullUrl);

      const response = await client.get(endpoint);

      console.log('Resources API response:', {
        status: response.status,
        data: response.data ? 'Received data' : 'No data'
      });

      if (response.data && Array.isArray(response.data.data)) {
        console.log(`Received ${response.data.data.length} resources`);
        setResources(prev => ({
          ...prev,
          [topicId]: response.data.data
        }));
      } else {
        console.warn('Unexpected resources response format:', response.data);
      }
    } catch (err) {
      console.error('Error fetching resources:', err);
      console.error('Error details:', {
        message: err.message,
        status: err.response?.status,
        response: err.response?.data
      });

      // Update error state for this specific topic
      const errorMsg = err.response?.data?.message || 'Failed to load resources. Please try again.';
      setError(prev => ({
        ...prev,
        [topicId]: errorMsg
      }));
    } finally {
      console.groupEnd();
      setLoadingResources(prev => ({ ...prev, [topicId]: false }));
    }
  };

  const handleTopicClick = (topicId) => {
    if (expandedTopic === topicId) {
      setExpandedTopic(null);
    } else {
      setExpandedTopic(topicId);
      fetchTopicResources(topicId);
    }
  };

  // Handle audio toggle
  const toggleAudio = (audioFileUrl, audioId) => {
    const audioElement = document.getElementById(`audio-${audioId}`);
    if (!audioElement) return;

    if (audioElement.paused) {
      // Convert to full AWS S3 URL before playing
      const fullAudioUrl = getAudioUrl(audioFileUrl);
      if (audioElement.src !== fullAudioUrl) {
        audioElement.src = fullAudioUrl;
      }
      audioElement.play().catch(error => {
        console.error('Audio play failed:', error);
      });
    } else {
      audioElement.pause();
    }
  };

  // Handle PDF view - navigate to ReadingPage
  const handleViewPdf = (resource) => {
    console.log('Navigating to ReadingPage with resource:', resource);
    if (!resource?.fileUrl) {
      console.error('No PDF URL provided in resource:', resource);
      return;
    }

    // Find the topic that contains this resource
    const parentTopic = topics.find(topic =>
      resources[topic.id]?.some(res => res.id === resource.id)
    );

    // Convert URLs to full AWS S3 URLs
    const fullPdfUrl = getPdfUrl(resource.fileUrl);
    const audioSources = resource.audioFiles?.map(audio => ({
      id: audio.id,
      title: audio.name,
      src: getAudioUrl(audio.fileUrl), // Convert audio URL
      duration: audio.duration
    })) || [];

    // Prepare data for ReadingPage
    const readingPageData = {
      pdfData: {
        id: resource.id,
        title: resource.name,
        pdfUrl: fullPdfUrl, // Use full AWS S3 URL
        audioSources: audioSources
      },
      // Pass course data with topics for better navigation
      courseData: {
        id: courseId,
        title: parentTopic?.title || 'Course',
        topics: topics.map(topic => ({
          id: topic.id,
          title: topic.title,
          // For now, we'll only set pdfUrl for the current resource's topic
          // In a full implementation, you might want to fetch all resources for all topics
          pdfUrl: topic.id === parentTopic?.id ? fullPdfUrl : null,
          audioSources: topic.id === parentTopic?.id ? audioSources : [],
          content: {
            heading: topic.title
          }
        }))
      }
    };

    // Store the data in session storage to pass to ReadingPage
    sessionStorage.setItem('readingPageData', JSON.stringify(readingPageData));

    // Navigate to ReadingPage
    navigate('/reading');
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <LoadingSpinner />
        <p>Loading topics...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <p className={styles.errorText}>{error}</p>
        <button
          className={styles.retryButton}
          onClick={() => window.location.reload()}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Topics</h1>
      <div className={styles.topicsList}>
        {topics.map((topic) => (
          <div key={topic.id} className={styles.topicCard}>
            <div
              className={`${styles.topicHeader} ${expandedTopic === topic.id ? styles.expanded : ''}`}
              onClick={() => handleTopicClick(topic.id)}
            >
              <h3 className={styles.topicTitle}>{topic.title}</h3>
              <span className={styles.arrowIcon}>
                {expandedTopic === topic.id ? '▼' : '▶'}
              </span>
            </div>

            {expandedTopic === topic.id && (
              <div className={styles.resourcesContainer}>
                {loadingResources[topic.id] ? (
                  <div className={styles.loadingResources}>
                    <LoadingSpinner size="small" />
                    <span>Loading resources...</span>
                  </div>
                ) : resources[topic.id]?.length > 0 ? (
                  <div className={styles.resourcesList}>
                    {resources[topic.id].map((resource) => (
                      <div key={resource.id} className={styles.resourceItem}>
                        <div className={styles.pdfContainer}>
                          <span className={styles.pdfIcon}>📄</span>
                          <div className={styles.resourceInfo}>
                            <span className={styles.resourceName}>{resource.name}</span>
                            <span className={styles.resourceMeta}>
                              {resource.fileSize && `${formatFileSize(resource.fileSize)} • `}
                              {resource.duration && `${Math.ceil(resource.duration / 60)} min`}
                            </span>
                          </div>
                          <button
                            className={styles.viewButton}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleViewPdf(resource);
                            }}
                            title="View PDF"
                          >
                            View PDF
                          </button>
                        </div>
                        {resource.audioFiles?.length > 0 && (
                          <div className={styles.audioList}>
                            {resource.audioFiles.map((audio) => (
                              <div key={audio.id} className={styles.audioItem}>
                                <div className={styles.audioInfo}>
                                  <span className={styles.audioIcon}>🔊</span>
                                  <div className={styles.audioDetails}>
                                    <span className={styles.audioName}>{audio.name}</span>
                                    <span className={styles.audioMeta}>
                                      {audio.duration && `${Math.ceil(audio.duration / 60)} min`}
                                      {audio.fileSize && ` • ${formatFileSize(audio.fileSize)}`}
                                    </span>
                                  </div>
                                </div>
                                <button
                                  className={`${styles.playButton} ${document.getElementById(`audio-${audio.id}`)?.paused ? '' : styles.playing}`}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    toggleAudio(audio.fileUrl, audio.id);
                                  }}
                                  title={document.getElementById(`audio-${audio.id}`)?.paused ? 'Play' : 'Pause'}
                                >
                                  {document.getElementById(`audio-${audio.id}`)?.paused ? '▶' : '❚❚'}
                                </button>
                                <audio
                                  id={`audio-${audio.id}`}
                                  src={getAudioUrl(audio.fileUrl)}
                                  preload="none"
                                />
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className={styles.noResources}>No resources available for this topic.</div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default TopicsPage;
