import { EyeIcon, EyeOffIcon } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { initializeServices } from '../../services/index.js';
import AnimationBox from '../../components/common/AnimationBox';
import Header from '../../components/layout/Header';
import '../../styles/LoginPage.css';
import '../../styles/CreatePassword.css';

const CreatePasswordPage = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [authService, setAuthService] = useState(null);

  const navigate = useNavigate();
  const location = useLocation();
  const email = location.state?.email || '';
  const verificationToken = location.state?.verificationToken || '';
  const purpose = location.state?.purpose || 'sign_up';

  // Configuration based on purpose
  const config = {
    sign_up: {
      title: 'Set A New Password',
      successRoute: '/user-details',
      backRoute: '/signup'
    },
    forgot_password: {
      title: 'Reset Your Password',
      successRoute: '/login',
      backRoute: '/forgot-password'
    }
  };

  const currentConfig = config[purpose] || config.sign_up;

  // Password strength validation
  const validatePassword = (password) => {
    const minLength = password.length >= 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return {
      minLength,
      hasUpperCase,
      hasLowerCase,
      hasNumbers,
      hasSpecialChar,
      isValid: minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar
    };
  };

  const passwordValidation = validatePassword(password);

  // Initialize services
  useEffect(() => {
    const initServices = async () => {
      try {
        const services = await initializeServices();
        setAuthService(services.authService);
      } catch (error) {
        console.error('Failed to initialize services:', error);
        setError('Failed to initialize application. Please refresh the page.');
      }
    };

    initServices();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (!email) {
      navigate(currentConfig.backRoute);
      return;
    }

    if (!authService) {
      setError('Service not initialized. Please refresh the page.');
      return;
    }

    if (!passwordValidation.isValid) {
      setError('Password does not meet the requirements.');
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match.');
      return;
    }

    setIsLoading(true);

    try {
      // Debug logging
      console.log(`🔐 ${currentConfig.title} Debug Info:`, {
        email,
        hasToken: !!verificationToken,
        tokenLength: verificationToken?.length || 0,
        purpose
      });

      let result;

      if (purpose === 'forgot_password') {
        // For password reset, call resetPassword API
        result = await authService.resetPassword(
          verificationToken,
          email,
          password,
          confirmPassword
        );
      } else {
        // For signup, use the existing setPassword method
        result = await authService.setPassword(
          email,
          password,
          confirmPassword,
          verificationToken
        );
      }

      console.log(`📋 ${currentConfig.title} Full Response:`, result);
      console.log(`📋 ${currentConfig.title} - Data Object:`, result.data);
      console.log(`📋 ${currentConfig.title} - Nested Data:`, result.data?.data);

      if (result.success) {
        console.log(`✅ ${currentConfig.title} completed successfully`);

        if (purpose === 'forgot_password') {
          // For password reset, navigate to login with success message
          navigate(currentConfig.successRoute, {
            state: {
              message: 'Password reset successfully! Please sign in with your new password.',
              email
            }
          });
        } else {
          // For signup, navigate to user details
          navigate(currentConfig.successRoute, {
            state: {
              email: email
            }
          });
        }
      } else {
        setError(result.error || `Failed to ${purpose === 'forgot_password' ? 'reset password' : 'create account'}. Please try again.`);
      }
    } catch (error) {
      console.error('Set password error:', error);
      // Display exact error message from backend
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          'Failed to create account';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="login-container">
      <Header />
      <div className="create-password-wrapper">
        <div className="create-password-layout">
          {/* Main Card - Form */}
          <AnimationBox className="create-password-box">
            <h4 className='h4 create-password-title'>{currentConfig.title}</h4>

            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="password">New Password:</label>
                <div className="password-wrapper">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter password..."
                    required
                  />
                  <button
                    type="button"
                    className="toggle-password"
                    onClick={() => setShowPassword((prev) => !prev)}
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                  >
                    {showPassword ? <EyeIcon size={20} /> : <EyeOffIcon size={20} />}
                  </button>
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="confirmPassword">Confirm Password:</label>
                <div className="password-wrapper">
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    id="confirmPassword"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm password..."
                    required
                  />
                  <button
                    type="button"
                    className="toggle-password"
                    onClick={() => setShowConfirmPassword((prev) => !prev)}
                    aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                  >
                    {showConfirmPassword ? <EyeIcon size={20} /> : <EyeOffIcon size={20} />}
                  </button>
                </div>
                {confirmPassword && password !== confirmPassword && (
                  <div className="error-message">Passwords do not match.</div>
                )}
              </div>

              {error && <div className="error-message">{error}</div>}

              <button
                type="submit"
                className="body3-bold login-button"
                disabled={!passwordValidation.isValid || password !== confirmPassword || isLoading}
              >
                {isLoading ? `${purpose === 'forgot_password' ? 'Resetting' : 'Creating'} ${purpose === 'forgot_password' ? 'Password' : 'Account'}...` : 'Confirm'}
              </button>
            </form>
          </AnimationBox>

          {/* Password Requirements - Outside the card */}
          <div className="password-requirements-sidebar">
            <div className="requirements-header">Must contain at least:</div>
            <ul className="requirements-list">
              <li className={passwordValidation.minLength ? 'valid' : 'invalid'}>
                <span className="requirement-icon">
                  {passwordValidation.minLength ? '✅' : '⭕'}
                </span>
                <span className="requirement-text">8 characters</span>
              </li>
              <li className={passwordValidation.hasLowerCase ? 'valid' : 'invalid'}>
                <span className="requirement-icon">
                  {passwordValidation.hasLowerCase ? '✅' : '⭕'}
                </span>
                <span className="requirement-text">1 lower case character</span>
              </li>
              <li className={passwordValidation.hasUpperCase ? 'valid' : 'invalid'}>
                <span className="requirement-icon">
                  {passwordValidation.hasUpperCase ? '✅' : '⭕'}
                </span>
                <span className="requirement-text">1 upper case character</span>
              </li>
              <li className={passwordValidation.hasNumbers ? 'valid' : 'invalid'}>
                <span className="requirement-icon">
                  {passwordValidation.hasNumbers ? '✅' : '⭕'}
                </span>
                <span className="requirement-text">1 number</span>
              </li>
              <li className={passwordValidation.hasSpecialChar ? 'valid' : 'invalid'}>
                <span className="requirement-icon">
                  {passwordValidation.hasSpecialChar ? '✅' : '⭕'}
                </span>
                <span className="requirement-text">1 special character</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreatePasswordPage;