import { render, screen, fireEvent } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';
import { vi } from 'vitest';
import Profile from '../Profile';
import { useAuthStore } from '../../stores/authStore';

// Mock the auth store
vi.mock('../../stores/authStore');

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock the SideNavBar component
vi.mock('../../components/layout/SideNavBar', () => ({
  default: ({ activeTab, onTabChange, onLogout, currentUser }) => (
    <div data-testid="side-navbar">
      <button onClick={() => onTabChange('home')} data-testid="home-tab">Home</button>
      <button onClick={() => onTabChange('profile')} data-testid="profile-tab" className={activeTab === 'profile' ? 'active' : ''}>Profile</button>
      <button onClick={onLogout} data-testid="logout-btn">Logout</button>
    </div>
  ),
}));

const renderProfile = (authState = {}) => {
  useAuthStore.mockReturnValue({
    currentUser: { id: 'test-user', email: '<EMAIL>', name: 'Test User' },
    isAuthenticated: true,
    logout: vi.fn(),
    ...authState,
  });

  return render(
    <BrowserRouter>
      <Profile />
    </BrowserRouter>
  );
};

describe('Profile Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render profile page with user information', () => {
    renderProfile();

    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('test-user')).toBeInTheDocument();
    expect(screen.getByText('Test User')).toBeInTheDocument();
  });

  it('should render sidebar with profile tab active', () => {
    renderProfile();

    const profileTab = screen.getByTestId('profile-tab');
    expect(profileTab).toHaveClass('active');
  });

  it('should navigate to dashboard when home tab is clicked', () => {
    renderProfile();

    const homeTab = screen.getByTestId('home-tab');
    fireEvent.click(homeTab);

    expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
  });

  it('should render profile action button', () => {
    renderProfile();

    expect(screen.getByText('Edit Profile')).toBeInTheDocument();
  });

  it('should redirect to login if not authenticated', () => {
    renderProfile({ isAuthenticated: false });

    expect(mockNavigate).toHaveBeenCalledWith('/login');
  });

  it('should handle logout correctly', () => {
    const mockLogout = vi.fn();
    renderProfile({ logout: mockLogout });

    const logoutBtn = screen.getByTestId('logout-btn');
    fireEvent.click(logoutBtn);

    expect(mockLogout).toHaveBeenCalled();
  });
});
