import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import NavigationBlockModal from '../NavigationBlockModal.jsx';

describe('NavigationBlockModal', () => {
  let mockOnStay;
  let mockOnLeave;

  beforeEach(() => {
    mockOnStay = vi.fn();
    mockOnLeave = vi.fn();
    
    // Mock document.body.classList methods
    document.body.classList.add = vi.fn();
    document.body.classList.remove = vi.fn();
  });

  afterEach(() => {
    vi.clearAllMocks();
    // Clean up any modal-open class that might have been added
    document.body.classList.remove('modal-open');
  });

  it('should not render when isOpen is false', () => {
    render(
      <NavigationBlockModal
        isOpen={false}
        onStay={mockOnStay}
        onLeave={mockOnLeave}
      />
    );

    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('should render when isOpen is true', () => {
    render(
      <NavigationBlockModal
        isOpen={true}
        onStay={mockOnStay}
        onLeave={mockOnLeave}
      />
    );

    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByText('Unsaved Changes')).toBeInTheDocument();
    expect(screen.getByText("Your changes haven't been saved. Are you sure you want to leave this page?")).toBeInTheDocument();
  });

  it('should render with custom title and message', () => {
    const customTitle = 'Custom Title';
    const customMessage = 'Custom message content';

    render(
      <NavigationBlockModal
        isOpen={true}
        onStay={mockOnStay}
        onLeave={mockOnLeave}
        title={customTitle}
        message={customMessage}
      />
    );

    expect(screen.getByText(customTitle)).toBeInTheDocument();
    expect(screen.getByText(customMessage)).toBeInTheDocument();
  });

  it('should call onStay when Stay on Page button is clicked', () => {
    render(
      <NavigationBlockModal
        isOpen={true}
        onStay={mockOnStay}
        onLeave={mockOnLeave}
      />
    );

    const stayButton = screen.getByText('Stay on Page');
    fireEvent.click(stayButton);

    expect(mockOnStay).toHaveBeenCalledTimes(1);
    expect(mockOnLeave).not.toHaveBeenCalled();
  });

  it('should call onLeave when Leave Without Saving button is clicked', () => {
    render(
      <NavigationBlockModal
        isOpen={true}
        onStay={mockOnStay}
        onLeave={mockOnLeave}
      />
    );

    const leaveButton = screen.getByText('Leave Without Saving');
    fireEvent.click(leaveButton);

    expect(mockOnLeave).toHaveBeenCalledTimes(1);
    expect(mockOnStay).not.toHaveBeenCalled();
  });

  it('should call onStay when Escape key is pressed', () => {
    render(
      <NavigationBlockModal
        isOpen={true}
        onStay={mockOnStay}
        onLeave={mockOnLeave}
      />
    );

    fireEvent.keyDown(document, { key: 'Escape' });

    expect(mockOnStay).toHaveBeenCalledTimes(1);
    expect(mockOnLeave).not.toHaveBeenCalled();
  });

  it('should call onStay when Enter key is pressed', () => {
    render(
      <NavigationBlockModal
        isOpen={true}
        onStay={mockOnStay}
        onLeave={mockOnLeave}
      />
    );

    fireEvent.keyDown(document, { key: 'Enter' });

    expect(mockOnStay).toHaveBeenCalledTimes(1);
    expect(mockOnLeave).not.toHaveBeenCalled();
  });

  it('should call onStay when clicking on overlay', () => {
    render(
      <NavigationBlockModal
        isOpen={true}
        onStay={mockOnStay}
        onLeave={mockOnLeave}
      />
    );

    const overlay = screen.getByRole('dialog');
    fireEvent.click(overlay);

    expect(mockOnStay).toHaveBeenCalledTimes(1);
    expect(mockOnLeave).not.toHaveBeenCalled();
  });

  it('should not call onStay when clicking on modal content', () => {
    render(
      <NavigationBlockModal
        isOpen={true}
        onStay={mockOnStay}
        onLeave={mockOnLeave}
      />
    );

    const modalContent = screen.getByText('Unsaved Changes').closest('.modal-content');
    fireEvent.click(modalContent);

    expect(mockOnStay).not.toHaveBeenCalled();
    expect(mockOnLeave).not.toHaveBeenCalled();
  });

  it('should add modal-open class to body when opened', () => {
    render(
      <NavigationBlockModal
        isOpen={true}
        onStay={mockOnStay}
        onLeave={mockOnLeave}
      />
    );

    expect(document.body.classList.add).toHaveBeenCalledWith('modal-open');
  });

  it('should remove modal-open class from body when closed', () => {
    const { rerender } = render(
      <NavigationBlockModal
        isOpen={true}
        onStay={mockOnStay}
        onLeave={mockOnLeave}
      />
    );

    rerender(
      <NavigationBlockModal
        isOpen={false}
        onStay={mockOnStay}
        onLeave={mockOnLeave}
      />
    );

    expect(document.body.classList.remove).toHaveBeenCalledWith('modal-open');
  });

  it('should have proper accessibility attributes', () => {
    render(
      <NavigationBlockModal
        isOpen={true}
        onStay={mockOnStay}
        onLeave={mockOnLeave}
      />
    );

    const dialog = screen.getByRole('dialog');
    expect(dialog).toHaveAttribute('aria-modal', 'true');
    expect(dialog).toHaveAttribute('aria-labelledby', 'modal-title');
    expect(dialog).toHaveAttribute('aria-describedby', 'modal-description');

    const title = screen.getByText('Unsaved Changes');
    expect(title).toHaveAttribute('id', 'modal-title');

    const stayButton = screen.getByText('Stay on Page');
    expect(stayButton).toHaveAttribute('aria-label', 'Stay on this page and continue editing');

    const leaveButton = screen.getByText('Leave Without Saving');
    expect(leaveButton).toHaveAttribute('aria-label', 'Leave this page without saving changes');
  });

  it('should focus the Stay on Page button by default', async () => {
    render(
      <NavigationBlockModal
        isOpen={true}
        onStay={mockOnStay}
        onLeave={mockOnLeave}
      />
    );

    const stayButton = screen.getByText('Stay on Page');
    
    await waitFor(() => {
      expect(stayButton).toHaveFocus();
    });
  });
});
