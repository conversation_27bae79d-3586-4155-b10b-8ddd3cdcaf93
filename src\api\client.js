/**
 * API Client - Axios instance with interceptors and configuration
 */
import axios from 'axios';
import env from '../config/environment.js';

class ApiClient {
  constructor() {
    this.axiosInstance = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the API client with environment configuration
   */
  async initialize() {
    if (this.isInitialized) {
      return this.axiosInstance;
    }

    const config = await env;

    // Create axios instance with default configuration
    this.axiosInstance = axios.create({
      baseURL: config.api.baseUrl,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
    this.isInitialized = true;

    // Set auth token if exists in storage
    await this.setAuthTokenFromStorage();

    return this.axiosInstance;
  }

  /**
   * Set auth token from storage if available
   */
  async setAuthTokenFromStorage() {
    try {
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const { state } = JSON.parse(authStorage);
        if (state?.currentUser?.token) {
          console.log(' Found existing auth token, setting in API client');
          await this.setAuthToken(state.currentUser.token);
        }
      }
    } catch (error) {
      console.warn('Failed to set auth token from storage:', error);
    }
  }

  /**
   * Setup request and response interceptors
   */
  setupInterceptors() {
    // Request interceptor for logging and token injection
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        console.log(`Making ${config.method?.toUpperCase()} request to: ${config.url}`);
        
        // Skip token check for auth endpoints
        if (config.url.includes('/auth/')) {
          return config;
        }
        
        // Get token from storage for each request
        try {
          const authStorage = localStorage.getItem('auth-storage');
          if (authStorage) {
            const { state } = JSON.parse(authStorage);
            if (state?.currentUser?.token) {
              config.headers.Authorization = `Bearer ${state.currentUser.token}`;
            }
          }
        } catch (error) {
          console.warn('Failed to set auth token in request interceptor:', error);
        }
        
        return config;
      },
      (error) => {
        console.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.axiosInstance.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        return this.handleResponseError(error);
      }
    );
  }

  /**
   * Handle response errors - preserves exact backend error messages
   */
  handleResponseError(error) {
    console.error('API request failed:', error);

    if (error.response) {
      // Server responded with error status
      const errorMessage = error.response.data?.message ||
                          error.response.data?.error ||
                          error.response.data?.detail ||
                          error.response.data?.errors?.[0]?.message ||
                          error.response.statusText ||
                          error.message ||
                          'An error occurred';

      // Handle specific status codes
      switch (error.response.status) {
        case 401:
          this.handleUnauthorized();
          break;
        case 403:
          console.warn('Access forbidden');
          break;
        case 429:
          console.warn('Rate limit exceeded');
          break;
        case 500:
          console.error('Server error');
          break;
      }

      throw new Error(errorMessage);
    } else if (error.request) {
      // Request was made but no response received
      throw new Error(error.message || 'Network error occurred');
    } else {
      // Something else happened
      throw new Error(error.message || 'An error occurred');
    }
  }

  /**
   * Handle unauthorized responses
   */
  handleUnauthorized() {
    // Clear stored auth token
    this.setAuthToken(null);

    // You can add additional logic here like redirecting to login
    console.warn('Authentication required - token may be expired');
  }

  /**
   * GET request
   */
  async get(endpoint, config = {}) {
    const instance = await this.getInstance();
    const response = await instance.get(endpoint, config);
    return response.data;
  }

  /**
   * POST request
   */
  async post(endpoint, data = null, config = {}) {
    const instance = await this.getInstance();
    const response = await instance.post(endpoint, data, config);
    return response.data;
  }

  /**
   * PUT request
   */
  async put(endpoint, data = null, config = {}) {
    const instance = await this.getInstance();
    const response = await instance.put(endpoint, data, config);
    return response.data;
  }

  /**
   * PATCH request
   */
  async patch(endpoint, data = null, config = {}) {
    const instance = await this.getInstance();
    const response = await instance.patch(endpoint, data, config);
    return response.data;
  }

  /**
   * DELETE request
   */
  async delete(endpoint, config = {}) {
    const instance = await this.getInstance();
    const response = await instance.delete(endpoint, config);
    return response.data;
  }

  /**
   * Set authorization header
   */
  async setAuthToken(token) {
    const instance = await this.getInstance();
    if (token) {
      instance.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete instance.defaults.headers.common['Authorization'];
    }
  }

  /**
   * Set custom header
   */
  async setHeader(key, value) {
    const instance = await this.getInstance();
    instance.defaults.headers.common[key] = value;
  }

  /**
   * Remove custom header
   */
  async removeHeader(key) {
    const instance = await this.getInstance();
    delete instance.defaults.headers.common[key];
  }

  /**
   * Get axios instance, initializing if necessary
   */
  async getInstance() {
    if (!this.isInitialized) {
      await this.initialize();
    }
    return this.axiosInstance;
  }
}

// Create singleton instance
const apiClient = new ApiClient();

export default apiClient;