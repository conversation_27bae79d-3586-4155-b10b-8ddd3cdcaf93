import { createBrowserRouter, Navigate } from "react-router-dom";
import { AppContent } from "../App";
import {
  LoginPage,
  SignupPage,
  OTPVerificationPage,
  CreatePasswordPage,
  ReadingPage,
  TermsPage,
  PrivacyPage,
  UserDetails,
  Dashboard,
  Profile,
  TopicsPage,
} from "../pages";
import { ErrorBoundary } from "../components/common";
import { PrivateRoute, PublicRoute, ProfileRequiredRoute } from "./";
import { useAuthStore } from "../stores";

const Root = () => {
  const { currentUser, isAuthenticated } = useAuthStore.getState();

  const getRedirectPath = () => {
    if (!isAuthenticated) {
      return "/login";
    }
    const isProfileComplete = currentUser?.is_profile_set === true;
    return isProfileComplete ? "/dashboard" : "/user-details";
  };

  return <AppContent />;
};

const router = createBrowserRouter([
  {
    path: "/",
    element: <Root />,
    errorElement: <ErrorBoundary />,
    children: [
      {
        index: true,
        element: (
          <Navigate
            to={
              (() => {
                const { currentUser, isAuthenticated } = useAuthStore.getState();
                if (!isAuthenticated) return "/login";
                return currentUser?.is_profile_set ? "/dashboard" : "/user-details";
              })()
            }
            replace
          />
        ),
      },
      {
        path: "login",
        element: (
          <PublicRoute>
            <LoginPage />
          </PublicRoute>
        ),
      },
      {
        path: "privacy",
        element: (
          <PublicRoute>
            <PrivacyPage />
          </PublicRoute>
        ),
      },
      {
        path: "terms",
        element: (
          <PublicRoute>
            <TermsPage />
          </PublicRoute>
        ),
      },
      {
        path: "signup",
        element: (
          <PublicRoute>
            <SignupPage />
          </PublicRoute>
        ),
      },
      {
        path: "otp-verification",
        element: (
          <PublicRoute>
            <OTPVerificationPage />
          </PublicRoute>
        ),
      },
      {
        path: "set-password",
        element: (
          <PublicRoute>
            <CreatePasswordPage />
          </PublicRoute>
        ),
      },
      {
        path: "forgot-password",
        element: (
          <PublicRoute>
            <SignupPage mode="forgot-password" />
          </PublicRoute>
        ),
      },
      {
        path: "user-details",
        element: (
          <PrivateRoute>
            <UserDetails />
          </PrivateRoute>
        ),
      },
      {
        path: "dashboard",
        element: (
          <ProfileRequiredRoute>
            <Dashboard />
          </ProfileRequiredRoute>
        ),
      },
      {
        path: "profile",
        element: (
          <ProfileRequiredRoute>
            <Profile />
          </ProfileRequiredRoute>
        ),
      },
      {
        path: "topics/:courseId",
        element: (
          <ProfileRequiredRoute>
            <TopicsPage />
          </ProfileRequiredRoute>
        ),
      },
      {
        path: "reading",
        element: (
          <ProfileRequiredRoute>
            <ReadingPage />
          </ProfileRequiredRoute>
        ),
      },
    ],
  },
]);

export default router;