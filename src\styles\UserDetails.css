/* UserDetails Page Styles */
.user-details-container {
  min-height: 100vh;
  background-color: var(--bg-color);
  display: flex;
  flex-direction: column;
}

.user-details-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.user-details-box {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.user-details-title {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--text-color);
}

.user-details-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  /* gap: 0.75rem; */
}

.form-label {
  color: var(--text-color);
  font-weight: 500;
  margin-bottom: 0;
}

/* Dropdown Styles */
.dropdown-select {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 1px solid var(--text-color);
  border-radius: 12px;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: var(--body3-size);
  line-height: var(--body3-line-height);
  font-weight: var(--font-weight-regular);
  cursor: pointer;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1rem;
  padding-right: 3rem;
  box-sizing: border-box;
  overflow:hidden;
}

.dropdown-select:focus {
  outline: none;
  border-color: var(--text-color);
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.dropdown-select:hover {
  border-color: #ccc;
}

.dropdown-select.error {
  border-color: #e74c3c;
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.1);
  animation: shake 0.3s ease-in-out;
}

.dropdown-select option {
  padding: 0.75rem;
  background-color: var(--bg-color);
  color: var(--text-color);
  
}

/* Segmented Control Styles (for Year and Semester) */
.segmented-control {
  display: flex;
  border: 1px solid var(--text-color);
  border-radius: 12px;
  overflow: hidden;
  background-color: var(--bg-color);
}

.segmented-option {
  flex: 1;
  padding: 0.875rem 1rem;
  background-color: transparent;
  color: var(--text-color);
  border: none;
  border-right: 1px solid var(--text-color);
  font-size: var(--body3-size);
  line-height: var(--body3-line-height);
  font-weight: var(--font-weight-regular);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  white-space: nowrap;
}

.segmented-option:last-child {
  border-right: none;
}

.segmented-option:hover:not(.selected) {
  background-color: #f8f8f8;
}

.segmented-option.selected {
  background-color: var(--text-color);
  color: var(--bg-color);
  font-size: var(--body3-size);
  line-height: var(--body3-line-height);
  font-weight: var(--font-weight-bold);
}

.segmented-option:focus {
  outline: none;
  position: relative;
  z-index: 1;
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.2);
}

/* Error Message Styles */
.error-message {
  color: #e74c3c;
}

/* Save Button Styles */
.save-button-container {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.save-button {
  background-color: var(--text-color);
  color: var(--bg-color);
  border: none;
  border-radius: 12px;
  padding: 0.875rem 2rem;
 
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 150px;
  width:100%;
}

.save-button:hover {
  background-color: var(--secondary-text-color, #333);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.save-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.save-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.2);
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--secondary-text-color, #666);
}

.save-button:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Animation for form validation */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-details-content {
    padding: 1rem;
  }
  
  .user-details-box {
    max-width: 100%;
  }
  
  /* .user-details-title {
    font-size: 1.5rem;
    line-height: 1.3;
  } */
  
  .dropdown-select {
    padding: 0.75rem 0.875rem;
    padding-right: 2.5rem;
    background-size: 0.875rem;
    background-position: right 0.875rem center;
  }
  
  .segmented-option {
    padding: 0.75rem 0.875rem;
    font-family: var(--font-family-default);
  font-size: var(--body3-size);
  line-height: var(--body3-line-height);
  font-weight: var(--font-weight-regular);
  }
  
  .save-button {
    padding: 0.75rem 1.5rem;
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .user-details-content {
    padding: 0.75rem;
  }
  
  .user-details-form {
    gap: 1.25rem;
  }
  
  .dropdown-select {
    padding: 0.625rem 0.75rem;
    padding-right: 2.25rem;
    font-size: var(--body3-size);
    line-height: var(--body3-line-height);
    font-weight: var(--font-weight-regular);
  }
  
  .segmented-option {
    padding: 0.625rem 0.75rem;
    font-size: var(--body3-size);
  line-height: var(--body3-line-height);
  font-weight: var(--font-weight-regular);
  }
  
  .save-button {
    padding: 0.625rem 1.25rem;
   
  }
}

/* Additional mobile optimization for very small screens */
@media (max-width: 360px) {
  .segmented-option {
    font-size: 0.85rem;
    padding: 0.5rem 0.5rem;
  }
}