import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import ReadingPage from '../ReadingPage.jsx';

// Mock the layout components
vi.mock('../../components/layout', () => ({
  CourseStructure: ({ topics, activeTopicId, onSelectTopic }) => (
    <div data-testid="course-structure">
      <div data-testid="topics-list">
        {topics?.map(topic => (
          <div 
            key={topic.id} 
            data-testid={`topic-${topic.id}`}
            onClick={() => onSelectTopic?.(topic.id)}
          >
            {topic.title}
          </div>
        ))}
      </div>
    </div>
  ),
  MainContent: ({ topic, textSize }) => (
    <div data-testid="main-content" data-text-size={textSize}>
      {topic?.pdfUrl ? (
        <div data-testid="pdf-viewer" data-pdf-url={topic.pdfUrl}>
          PDF: {topic.title}
        </div>
      ) : (
        <div data-testid="no-pdf">No PDF available</div>
      )}
    </div>
  ),
  ToolsPanel: ({ textSize, onTextSizeChange, currentTopic }) => (
    <div data-testid="tools-panel">
      <button 
        onClick={() => onTextSizeChange?.('large')} 
        data-testid="text-size-button"
      >
        Change Text Size
      </button>
      <div data-testid="current-topic">{currentTopic?.title}</div>
    </div>
  ),
}));

// Mock stores
vi.mock('../../stores', () => ({
  useAudioStore: (selector) => {
    const store = {
      initialize: vi.fn(() => () => {}),
      loadPagePlaylist: vi.fn(),
    };
    return selector(store);
  },
}));

// Mock hooks
vi.mock('../../hooks', () => ({
  useLocalStorage: vi.fn(() => ['normal', vi.fn()]),
}));

describe('PDF Viewing Integration', () => {
  const mockPdfData = {
    id: 'pdf-123',
    title: 'Test PDF Document',
    pdfUrl: 'https://example.com/test.pdf',
    audioSources: [
      {
        id: 'audio-1',
        title: 'Audio Track 1',
        src: 'https://example.com/audio1.mp3',
        duration: 300
      }
    ]
  };

  const mockCourseData = {
    id: 'course-456',
    title: 'Test Course'
  };

  beforeEach(() => {
    // Clear sessionStorage before each test
    sessionStorage.clear();
    vi.clearAllMocks();
  });

  afterEach(() => {
    sessionStorage.clear();
  });

  const renderWithRouter = (component) => {
    return render(
      <MemoryRouter>
        {component}
      </MemoryRouter>
    );
  };

  describe('Single PDF Viewing', () => {
    it('should render PDF viewer when valid PDF data is provided', async () => {
      // Simulate data from TopicsPage
      const readingPageData = {
        pdfData: mockPdfData,
        courseData: mockCourseData
      };
      
      sessionStorage.setItem('readingPageData', JSON.stringify(readingPageData));

      renderWithRouter(<ReadingPage />);

      // Wait for component to load data from sessionStorage
      await waitFor(() => {
        expect(screen.getByTestId('pdf-viewer')).toBeInTheDocument();
      });

      // Verify PDF is displayed
      expect(screen.getByTestId('pdf-viewer')).toHaveAttribute('data-pdf-url', mockPdfData.pdfUrl);
      expect(screen.getByTestId('pdf-viewer')).toHaveTextContent('PDF: Test PDF Document');
    });

    it('should show loading state initially', () => {
      renderWithRouter(<ReadingPage />);

      expect(screen.getByText('Loading PDF...')).toBeInTheDocument();
    });

    it('should show error state when no PDF data is available', async () => {
      sessionStorage.setItem('readingPageData', JSON.stringify({}));

      renderWithRouter(<ReadingPage />);

      await waitFor(() => {
        expect(screen.getByText('No PDF data available')).toBeInTheDocument();
      });

      expect(screen.getByText('Go Back')).toBeInTheDocument();
    });

    it('should create minimal course structure for single PDF', async () => {
      const readingPageData = {
        pdfData: mockPdfData,
        courseData: mockCourseData
      };
      
      sessionStorage.setItem('readingPageData', JSON.stringify(readingPageData));

      renderWithRouter(<ReadingPage />);

      await waitFor(() => {
        expect(screen.getByTestId('course-structure')).toBeInTheDocument();
      });

      // Should show the PDF as a topic in the course structure
      expect(screen.getByTestId(`topic-${mockPdfData.id}`)).toBeInTheDocument();
      expect(screen.getByTestId(`topic-${mockPdfData.id}`)).toHaveTextContent(mockPdfData.title);
    });
  });

  describe('Course Structure Integration', () => {
    it('should handle full course data with multiple topics', async () => {
      const fullCourseData = {
        id: 'course-456',
        title: 'Test Course',
        topics: [
          {
            id: 'topic-1',
            title: 'Topic 1',
            pdfUrl: 'https://example.com/topic1.pdf',
            audioSources: [],
            content: { heading: 'Topic 1' }
          },
          {
            id: 'topic-2', 
            title: 'Topic 2',
            pdfUrl: 'https://example.com/topic2.pdf',
            audioSources: [],
            content: { heading: 'Topic 2' }
          }
        ]
      };

      const readingPageData = {
        pdfData: mockPdfData,
        courseData: fullCourseData
      };
      
      sessionStorage.setItem('readingPageData', JSON.stringify(readingPageData));

      renderWithRouter(<ReadingPage />);

      await waitFor(() => {
        expect(screen.getByTestId('course-structure')).toBeInTheDocument();
      });

      // Should show all topics
      expect(screen.getByTestId('topic-topic-1')).toBeInTheDocument();
      expect(screen.getByTestId('topic-topic-2')).toBeInTheDocument();
    });
  });
});
