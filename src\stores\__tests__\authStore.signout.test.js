import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock the auth service
const mockSignout = vi.fn();
const mockLogout = vi.fn();
const mockSetAuthToken = vi.fn();

vi.mock('../../services/authService.js', () => ({
  default: class MockAuthService {
    constructor() {
      this.apiClient = {
        setAuthToken: mockSetAuthToken
      };
    }
    
    signout = mockSignout;
    logout = mockLogout;
  }
}));

// Import after mocking
import { useAuthStore } from '../authStore.js';

describe('AuthStore Signout Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset store state
    useAuthStore.setState({
      currentUser: null,
      isAuthenticated: false
    });
  });

  describe('logout method with signout integration', () => {
    it('should use signout API when user email is available', async () => {
      // Setup authenticated user
      const testUser = {
        id: 'test-user-id',
        email: '<EMAIL>',
        token: 'test-token'
      };

      useAuthStore.setState({
        currentUser: testUser,
        isAuthenticated: true
      });

      mockSignout.mockResolvedValue({ success: true });

      // Call logout
      const { logout } = useAuthStore.getState();
      await logout();

      // Verify signout was called with user email
      expect(mockSignout).toHaveBeenCalledWith('<EMAIL>');
      expect(mockLogout).not.toHaveBeenCalled();

      // Verify state was cleared
      const state = useAuthStore.getState();
      expect(state.currentUser).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    });

    it('should fallback to legacy logout when no user email available', async () => {
      // Setup authenticated user without email
      const testUser = {
        id: 'test-user-id',
        token: 'test-token'
        // No email property
      };

      useAuthStore.setState({
        currentUser: testUser,
        isAuthenticated: true
      });

      mockLogout.mockResolvedValue({ success: true });

      // Call logout
      const { logout } = useAuthStore.getState();
      await logout();

      // Verify legacy logout was called
      expect(mockLogout).toHaveBeenCalled();
      expect(mockSignout).not.toHaveBeenCalled();

      // Verify state was cleared
      const state = useAuthStore.getState();
      expect(state.currentUser).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    });

    it('should fallback to legacy logout when user is null', async () => {
      // Setup no user
      useAuthStore.setState({
        currentUser: null,
        isAuthenticated: true // Edge case
      });

      mockLogout.mockResolvedValue({ success: true });

      // Call logout
      const { logout } = useAuthStore.getState();
      await logout();

      // Verify legacy logout was called
      expect(mockLogout).toHaveBeenCalled();
      expect(mockSignout).not.toHaveBeenCalled();

      // Verify state was cleared
      const state = useAuthStore.getState();
      expect(state.currentUser).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    });

    it('should clear state even when signout API fails', async () => {
      // Setup authenticated user
      const testUser = {
        id: 'test-user-id',
        email: '<EMAIL>',
        token: 'test-token'
      };

      useAuthStore.setState({
        currentUser: testUser,
        isAuthenticated: true
      });

      // Mock signout to throw error
      mockSignout.mockRejectedValue(new Error('Network error'));

      // Call logout
      const { logout } = useAuthStore.getState();
      await logout();

      // Verify signout was attempted
      expect(mockSignout).toHaveBeenCalledWith('<EMAIL>');

      // Verify state was still cleared despite error
      const state = useAuthStore.getState();
      expect(state.currentUser).toBeNull();
      expect(state.isAuthenticated).toBe(false);

      // Verify token was cleared
      expect(mockSetAuthToken).toHaveBeenCalledWith(null);
    });

    it('should handle empty email string', async () => {
      // Setup authenticated user with empty email
      const testUser = {
        id: 'test-user-id',
        email: '',
        token: 'test-token'
      };

      useAuthStore.setState({
        currentUser: testUser,
        isAuthenticated: true
      });

      mockLogout.mockResolvedValue({ success: true });

      // Call logout
      const { logout } = useAuthStore.getState();
      await logout();

      // Should fallback to legacy logout for empty email
      expect(mockLogout).toHaveBeenCalled();
      expect(mockSignout).not.toHaveBeenCalled();
    });

    it('should handle whitespace-only email', async () => {
      // Setup authenticated user with whitespace email
      const testUser = {
        id: 'test-user-id',
        email: '   ',
        token: 'test-token'
      };

      useAuthStore.setState({
        currentUser: testUser,
        isAuthenticated: true
      });

      mockSignout.mockResolvedValue({ success: true });

      // Call logout
      const { logout } = useAuthStore.getState();
      await logout();

      // Should use signout even with whitespace email (API will handle validation)
      expect(mockSignout).toHaveBeenCalledWith('   ');
      expect(mockLogout).not.toHaveBeenCalled();
    });

    it('should maintain backward compatibility', async () => {
      // This test ensures the logout method signature hasn't changed
      const { logout } = useAuthStore.getState();
      
      // Should be callable without parameters
      expect(typeof logout).toBe('function');
      expect(logout.length).toBe(0); // No required parameters
    });
  });
});
