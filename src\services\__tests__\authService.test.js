import { describe, it, expect, beforeEach, vi } from 'vitest';

// Unmock AuthService to use the real implementation instead of the global mock
vi.unmock('../authService.js');

// Mock the API client
vi.mock('../../api/index.js', () => ({
  apiClient: {
    post: vi.fn(),
    get: vi.fn(),
    setAuthToken: vi.fn(),
  },
  API_ENDPOINTS: {
    auth: {
      emailVerification: '/auth/email-verification',
      validateOTP: '/auth/validate-otp',
      setPassword: '/auth/set-password',
      createAccount: '/auth/create-account',
      signin: '/auth/signin',
      login: '/auth/login',
      logout: '/auth/logout',
      signout: '/auth/signout',
      resendVerification: '/auth/resend-verification',
    }
  },
  withErrorHandling: vi.fn(),
}));

// Mock the crypto utilities
vi.mock('../../utils/crypto.js', () => ({
  hashPasswordSHA256: vi.fn(),
  encryptPassword: vi.fn(),
}));

// Mock the device helper
vi.mock('../../utils/deviceHelper.js', () => ({
  generateDeviceIdWithTimestamp: vi.fn(() => 'SEA-*************'),
}));

// Import AuthService after mocks are set up
import AuthService from '../authService.js';

describe('AuthService', () => {
  let authService;
  let mockApiClient;
  let mockWithErrorHandling;
  let mockEncryptPassword;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Import the mocked modules
    const { apiClient, withErrorHandling } = await import('../../api/index.js');
    const { encryptPassword } = await import('../../utils/crypto.js');
    mockApiClient = apiClient;
    mockWithErrorHandling = withErrorHandling;
    mockEncryptPassword = encryptPassword;

    // Setup default mock for password encryption
    mockEncryptPassword.mockResolvedValue('encrypted_password_hash');

    // Create a new instance of AuthService
    authService = new AuthService();

    // Ensure the authService has the apiClient
    authService.apiClient = mockApiClient;
  });

  describe('sendEmailVerification', () => {
    it('should send email verification successfully', async () => {
      const mockResponse = { success: true, data: { message: 'Verification email sent' } };
      mockWithErrorHandling.mockResolvedValue(mockResponse);

      const result = await authService.sendEmailVerification('<EMAIL>', 'captcha-token');

      expect(mockWithErrorHandling).toHaveBeenCalledWith(
        expect.any(Function),
        { operation: 'sendEmailVerification', email: '<EMAIL>' }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle email verification failure', async () => {
      const mockError = { success: false, error: 'API Error' };
      mockWithErrorHandling.mockResolvedValue(mockError);

      const result = await authService.sendEmailVerification('<EMAIL>', 'captcha-token');

      expect(result).toEqual(mockError);
    });
  });

  describe('validateOTP', () => {
    it('should validate OTP successfully with all parameters', async () => {
      const mockResponse = { success: true, data: { message: 'OTP validated successfully' } };
      mockWithErrorHandling.mockResolvedValue(mockResponse);

      const result = await authService.validateOTP('<EMAIL>', '123456', 'token123', 'sign_up');

      expect(mockWithErrorHandling).toHaveBeenCalledWith(
        expect.any(Function),
        { operation: 'validateOTP', email: '<EMAIL>', purpose: 'sign_up' }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should validate OTP with default purpose', async () => {
      const mockResponse = { success: true, data: { message: 'OTP validated successfully' } };
      mockWithErrorHandling.mockResolvedValue(mockResponse);

      const result = await authService.validateOTP('<EMAIL>', '123456', 'token123');

      expect(mockWithErrorHandling).toHaveBeenCalledWith(
        expect.any(Function),
        { operation: 'validateOTP', email: '<EMAIL>', purpose: 'sign_up' }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle OTP validation failure', async () => {
      const mockError = { success: false, error: 'Invalid OTP' };
      mockWithErrorHandling.mockResolvedValue(mockError);

      const result = await authService.validateOTP('<EMAIL>', '123456', 'token123');

      expect(result).toEqual(mockError);
    });
  });

  describe('setPassword', () => {
    it('should set password successfully without token', async () => {
      const mockResponse = { success: true, data: { message: 'Password set successfully' } };
      mockWithErrorHandling.mockResolvedValue(mockResponse);

      const result = await authService.setPassword('<EMAIL>', 'password123', 'password123');

      expect(mockWithErrorHandling).toHaveBeenCalledWith(
        expect.any(Function),
        { operation: 'setPassword', email: '<EMAIL>', hasToken: false }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should set password successfully with token', async () => {
      const mockResponse = { success: true, data: { message: 'Password set successfully' } };
      mockWithErrorHandling.mockResolvedValue(mockResponse);

      const result = await authService.setPassword('<EMAIL>', 'password123', 'password123', 'token123');

      expect(mockWithErrorHandling).toHaveBeenCalledWith(
        expect.any(Function),
        { operation: 'setPassword', email: '<EMAIL>', hasToken: true }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle password mismatch', async () => {
      const result = await authService.setPassword('<EMAIL>', 'password123', 'password456');

      expect(result).toEqual({
        success: false,
        error: 'Passwords do not match'
      });

      // Should not call API if passwords don't match
      expect(mockWithErrorHandling).not.toHaveBeenCalled();
    });

    it('should handle password mismatch with token', async () => {
      const result = await authService.setPassword('<EMAIL>', 'password123', 'password456', 'token123');

      expect(result).toEqual({
        success: false,
        error: 'Passwords do not match'
      });

      // Should not call API if passwords don't match
      expect(mockWithErrorHandling).not.toHaveBeenCalled();
    });

    it('should handle set password failure', async () => {
      const mockError = { success: false, error: 'Password requirements not met' };
      mockWithErrorHandling.mockResolvedValue(mockError);

      const result = await authService.setPassword('<EMAIL>', 'weak', 'weak');

      expect(result).toEqual(mockError);
    });
  });

  describe('signout', () => {
    it('should sign out user successfully with device tracking', async () => {
      const mockResponse = { success: true, data: { message: 'Signed out successfully' } };
      mockWithErrorHandling.mockResolvedValue(mockResponse);

      const result = await authService.signout('<EMAIL>');

      expect(mockWithErrorHandling).toHaveBeenCalledWith(
        expect.any(Function),
        { operation: 'signout', email: '<EMAIL>' }
      );

      // Verify that setAuthToken was called to clear the token
      expect(mockApiClient.setAuthToken).toHaveBeenCalledWith(null);

      expect(result).toEqual(mockResponse);
    });

    it('should make GET request with correct query parameters', async () => {
      const mockResponse = { success: true, data: { message: 'Signed out successfully' } };
      mockWithErrorHandling.mockImplementation(async (fn) => {
        await fn(); // Execute the function to test the API call
        return mockResponse;
      });

      await authService.signout('<EMAIL>');

      // Verify GET request was made with correct endpoint and query parameters
      expect(mockApiClient.get).toHaveBeenCalledWith(
        expect.stringMatching(/^\/auth\/signout\?email=user%40example\.com&device=SEA-\d+$/)
      );
    });

    it('should clear auth token even if API call fails', async () => {
      const mockError = { success: false, error: 'Network error' };
      mockWithErrorHandling.mockResolvedValue(mockError);

      const result = await authService.signout('<EMAIL>');

      // Should still clear the auth token
      expect(mockApiClient.setAuthToken).toHaveBeenCalledWith(null);

      // Should return success with local message when API fails
      expect(result).toEqual({
        success: true,
        data: { message: 'Signed out locally' }
      });
    });

    it('should handle missing email parameter', async () => {
      const mockResponse = { success: true, data: { message: 'Signed out successfully' } };
      mockWithErrorHandling.mockResolvedValue(mockResponse);

      const result = await authService.signout('');

      expect(mockWithErrorHandling).toHaveBeenCalledWith(
        expect.any(Function),
        { operation: 'signout', email: '' }
      );

      expect(result).toEqual(mockResponse);
    });
  });

});
