import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { courseService } from '../../services/courseService';
import { topicService } from '../../services/topicService';
import { toast } from 'react-toastify';
import { LoadingSpinner } from '../../components';
import { getAudioUrl, getPdfUrl } from '../../utils/mediaUtils';
import styles from './CourseDetailPage.module.css';

// Utility function to format file size
const formatFileSize = (bytes) => {
  if (!bytes) return '';
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

const CourseDetailPage = () => {
  const { courseId } = useParams();
  const navigate = useNavigate();
  const [course, setCourse] = useState(null);
  const [topics, setTopics] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('curriculum');
  const [expandedSubjects, setExpandedSubjects] = useState({});
  const [expandedTopics, setExpandedTopics] = useState({});
  const [resources, setResources] = useState({});
  const [loadingResources, setLoadingResources] = useState({});

  useEffect(() => {
    const fetchCourseData = async () => {
      if (!courseId) return;

      try {
        setLoading(true);

        // Fetch course details and topics in parallel
        const [courseResponse, topicsResponse] = await Promise.all([
          courseService.getCourseDetails(courseId),
          topicService.getTopicsByCourseId(courseId)
        ]);

        if (courseResponse && courseResponse.data) {
          console.log('Full Course API Response:', courseResponse);
          console.log('courseResponse.data type:', typeof courseResponse.data);
          console.log('courseResponse.data is array:', Array.isArray(courseResponse.data));

          // Your API response structure: { message: "...", data: [courseObject], count: 1, statusCode: 200 }
          let courseData = null;

          if (Array.isArray(courseResponse.data) && courseResponse.data.length > 0) {
            // Response.data is directly an array of courses
            courseData = courseResponse.data[0];
            console.log('✅ Course data extracted from direct array:', courseData);
          } else if (courseResponse.data.data && Array.isArray(courseResponse.data.data) && courseResponse.data.data.length > 0) {
            // Response has nested data: { data: { data: [courseObject] } }
            courseData = courseResponse.data.data[0];
            console.log('✅ Course data extracted from nested array:', courseData);
          } else {
            console.log('❌ Unexpected response structure:', courseResponse.data);
            courseData = courseResponse.data;
          }

          console.log('🎯 Final Course Data to set:', courseData);
          console.log('🎯 Course title:', courseData?.title);
          console.log('🎯 Course description:', courseData?.description);
          console.log('🎯 Course image:', courseData?.displayPicture);

          setCourse(courseData);
        }

        // Handle topics response - check for nested data structure
        if (topicsResponse && topicsResponse.data) {
          // Check if data is nested (response.data.data) or direct (response.data)
          const topicsData = Array.isArray(topicsResponse.data.data)
            ? topicsResponse.data.data
            : Array.isArray(topicsResponse.data)
            ? topicsResponse.data
            : [];
          setTopics(topicsData);
        }

      } catch (error) {
        console.error('Error fetching course data:', error);
        toast.error('Failed to load course details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchCourseData();
  }, [courseId]);

  const handleBack = () => {
    navigate('/dashboard');
  };

  const toggleSubject = (subjectId) => {
    setExpandedSubjects(prev => ({
      ...prev,
      [subjectId]: !prev[subjectId]
    }));
  };

  const toggleTopic = (topicId) => {
    setExpandedTopics(prev => ({
      ...prev,
      [topicId]: !prev[topicId]
    }));

    // Fetch resources when expanding a topic
    if (!expandedTopics[topicId] && !resources[topicId]) {
      fetchTopicResources(topicId);
    }
  };

  // Fetch resources for a topic when expanded
  const fetchTopicResources = async (topicId) => {
    if (resources[topicId]) return; // Skip if already loaded

    try {
      setLoadingResources(prev => ({ ...prev, [topicId]: true }));
      const response = await topicService.getTopicResources(topicId);

      if (response && response.data) {
        const resourcesData = Array.isArray(response.data.data)
          ? response.data.data
          : Array.isArray(response.data)
          ? response.data
          : [];
        setResources(prev => ({
          ...prev,
          [topicId]: resourcesData
        }));
      }
    } catch (error) {
      console.error('Error fetching resources:', error);
      toast.error('Failed to load resources. Please try again.');
    } finally {
      setLoadingResources(prev => ({ ...prev, [topicId]: false }));
    }
  };

  // Handle PDF viewing
  const handleViewPdf = (resource) => {
    const parentTopic = topics.find(topic =>
      resources[topic.id]?.some(r => r.id === resource.id)
    );

    if (!resource.fileUrl) {
      toast.error('PDF file not available');
      return;
    }

    // Get full PDF URL
    const fullPdfUrl = getPdfUrl(resource.fileUrl);

    // Get audio sources for this resource
    const audioSources = resource.audioFiles?.map(audio => getAudioUrl(audio.fileUrl)) || [];

    // Prepare data for ReadingPage
    const readingPageData = {
      pdfData: {
        id: resource.id,
        title: resource.name,
        pdfUrl: fullPdfUrl,
        audioSources: audioSources
      },
      courseData: {
        id: courseId,
        title: parentTopic?.title || course?.title || 'Course',
        topics: topics.map(topic => ({
          id: topic.id,
          title: topic.title,
          pdfUrl: topic.id === parentTopic?.id ? fullPdfUrl : null,
          audioSources: topic.id === parentTopic?.id ? audioSources : [],
          content: {
            heading: topic.title
          }
        }))
      }
    };

    // Store the data in session storage to pass to ReadingPage
    sessionStorage.setItem('readingPageData', JSON.stringify(readingPageData));

    // Navigate to ReadingPage
    navigate('/reading');
  };

  // Handle audio play/pause
  const toggleAudio = (_, audioId) => {
    const audioElement = document.getElementById(`audio-${audioId}`);
    if (audioElement) {
      if (audioElement.paused) {
        // Pause all other audio elements
        document.querySelectorAll('audio').forEach(audio => {
          if (audio !== audioElement) {
            audio.pause();
          }
        });
        audioElement.play();
      } else {
        audioElement.pause();
      }
    }
  };

  // Group topics by subject (assuming topics have a subject field)
  const groupedTopics = topics.reduce((acc, topic) => {
    const subjectName = topic.subject || course?.title || 'General';
    if (!acc[subjectName]) {
      acc[subjectName] = [];
    }
    acc[subjectName].push(topic);
    return acc;
  }, {});

  // Auto-expand the first subject if there are topics
  useEffect(() => {
    if (Object.keys(groupedTopics).length > 0 && Object.keys(expandedSubjects).length === 0) {
      const firstSubject = Object.keys(groupedTopics)[0];
      setExpandedSubjects({ [firstSubject]: true });
    }
  }, [groupedTopics]);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <LoadingSpinner />
        <p>Loading course details...</p>
      </div>
    );
  }

  if (!course) {
    return (
      <div className={styles.errorContainer}>
        <p className={styles.errorText}>Course not found</p>
        <p className={`body3 ${styles.debugText}`}>
          Debug: courseId = {courseId}, loading = {loading.toString()}
        </p>
        <button className={styles.backButton} onClick={handleBack}>
          BACK
        </button>
      </div>
    );
  }

  // Debug log for render
  console.log('🎨 Rendering CourseDetailPage with course:', course);

  return (
    <div className={styles.container}>
      {/* Header with course image, title and back button */}
      <div className={styles.header}>
        <div className={styles.courseInfo}>
          <div className={styles.courseImageContainer}>
            {course.displayPicture ? (
              <img
                src={course.displayPicture}
                alt={course.title}
                className={styles.courseImage}
                onError={(e) => {
                  console.log('❌ Image failed to load:', course.displayPicture);
                  e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik02MCA0MEMyNi44NjI5IDQwIDQwIDUzLjEzNzEgNDAgNjBDNDAgNjYuODYyOSA1My4xMzcxIDgwIDYwIDgwQzY2Ljg2MjkgODAgODAgNjYuODYyOSA4MCA2MEM4MCA1My4xMzcxIDY2Ljg2MjkgNDAgNjAgNDBaIiBmaWxsPSIjOUI1OUI2Ii8+Cjx0ZXh0IHg9IjYwIiB5PSI5NSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIiBmaWxsPSIjNkI3MjgwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5Db3Vyc2U8L3RleHQ+Cjwvc3ZnPgo=';
                  e.target.alt = 'Course placeholder';
                }}
                onLoad={() => {
                  console.log('✅ Image loaded successfully:', course.displayPicture);
                }}
              />
            ) : (
              <div className={styles.coursePlaceholder}>
                <span className={styles.placeholderText}>📚</span>
              </div>
            )}
          </div>
          <div className={styles.titleSection}>
            <h1 className={`h2 ${styles.courseTitle}`}>{course.title}</h1>
            <div className={styles.underline}></div>
            {/* Show course metadata */}
            <div className={styles.courseMetadata}>
              <span className={`body3 ${styles.coursePrice}`}>
                ₹{course.offerPrice} {course.originalPrice && course.originalPrice !== course.offerPrice && (
                  <span className={styles.originalPrice}>₹{course.originalPrice}</span>
                )}
              </span>
              {course.tags && (
                <div className={styles.courseTags}>
                  {course.tags.split(',').slice(0, 3).map((tag, index) => (
                    <span key={index} className={styles.courseTag}>{tag.trim()}</span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
        <button className={styles.backButton} onClick={handleBack}>
          BACK
        </button>
      </div>

      {/* Tab Navigation */}
      <div className={styles.tabNavigation}>
        <button
          className={`${styles.tab} ${activeTab === 'description' ? styles.activeTab : ''}`}
          onClick={() => setActiveTab('description')}
        >
          Description
        </button>
        <button
          className={`${styles.tab} ${activeTab === 'curriculum' ? styles.activeTab : ''}`}
          onClick={() => setActiveTab('curriculum')}
        >
          Curriculum
        </button>
      </div>

      {/* Tab Content */}
      <div className={styles.tabContent}>
        {activeTab === 'description' && (
          <div className={styles.descriptionContent}>
            <h2 className={`h3 ${styles.sectionTitle}`}>Course Description</h2>
            <div className={`body2 ${styles.descriptionText}`}>
              {(() => {
                console.log('📝 Description render - Course object:', course);
                console.log('📝 Description value:', course?.description);
                console.log('📝 Description exists:', !!course?.description);

                if (course?.description && course.description.trim() !== '') {
                  return <p>{course.description}</p>;
                } else {
                  return <p>No description available for this course.</p>;
                }
              })()}
            </div>
          </div>
        )}

        {activeTab === 'curriculum' && (
          <div className={styles.curriculumContent}>
            <h2 className={`h3 ${styles.sectionTitle}`}>Subjects</h2>
            <div className={styles.subjectsList}>
              {Object.entries(groupedTopics).map(([subjectName, subjectTopics]) => (
                <div key={subjectName} className={styles.subjectCard}>
                  <div
                    className={styles.subjectHeader}
                    onClick={() => toggleSubject(subjectName)}
                  >
                    <div className={styles.subjectInfo}>
                      <span className={styles.folderIcon}>📁</span>
                      <span className={styles.subjectName}>{subjectName}</span>
                    </div>
                    <span className={`${styles.expandIcon} ${expandedSubjects[subjectName] ? styles.expanded : ''}`}>
                      ▶
                    </span>
                  </div>

                  {expandedSubjects[subjectName] && (
                    <div className={styles.chaptersContainer}>
                      {subjectTopics.map((topic, index) => (
                        <div key={topic.id} className={styles.topicCard}>
                          <div
                            className={`${styles.chapterItem} ${expandedTopics[topic.id] ? styles.expanded : ''}`}
                            onClick={() => toggleTopic(topic.id)}
                          >
                            <div className={styles.topicInfo}>
                              <span className={styles.chapterNumber}>Chapter : {index + 1}</span>
                              <span className={styles.chapterTitle}>{topic.title}</span>
                            </div>
                            <span className={`${styles.expandIcon} ${expandedTopics[topic.id] ? styles.expanded : ''}`}>
                              ▶
                            </span>
                          </div>

                          {expandedTopics[topic.id] && (
                            <div className={styles.resourcesContainer}>
                              {loadingResources[topic.id] ? (
                                <div className={styles.loadingResources}>
                                  <LoadingSpinner size="small" />
                                  <span>Loading resources...</span>
                                </div>
                              ) : resources[topic.id]?.length > 0 ? (
                                <div className={styles.resourcesList}>
                                  {resources[topic.id].map((resource) => (
                                    <div key={resource.id} className={styles.resourceItem}>
                                      <div className={styles.pdfContainer}>
                                        <span className={styles.pdfIcon}>📄</span>
                                        <div className={styles.resourceInfo}>
                                          <span className={styles.resourceName}>{resource.name}</span>
                                          <span className={styles.resourceMeta}>
                                            {resource.fileSize && `${formatFileSize(resource.fileSize)} • `}
                                            {resource.duration && `${Math.ceil(resource.duration / 60)} min`}
                                          </span>
                                        </div>
                                        <button
                                          className={styles.viewButton}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleViewPdf(resource);
                                          }}
                                          title="View PDF"
                                        >
                                          View PDF
                                        </button>
                                      </div>
                                      {resource.audioFiles?.length > 0 && (
                                        <div className={styles.audioList}>
                                          {resource.audioFiles.map((audio) => (
                                            <div key={audio.id} className={styles.audioItem}>
                                              <div className={styles.audioInfo}>
                                                <span className={styles.audioIcon}>🔊</span>
                                                <div className={styles.audioDetails}>
                                                  <span className={styles.audioName}>{audio.name}</span>
                                                  <span className={styles.audioMeta}>
                                                    {audio.duration && `${Math.ceil(audio.duration / 60)} min`}
                                                    {audio.fileSize && ` • ${formatFileSize(audio.fileSize)}`}
                                                  </span>
                                                </div>
                                              </div>
                                              <button
                                                className={`${styles.playButton} ${document.getElementById(`audio-${audio.id}`)?.paused ? '' : styles.playing}`}
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  toggleAudio(audio.fileUrl, audio.id);
                                                }}
                                                title={document.getElementById(`audio-${audio.id}`)?.paused ? 'Play' : 'Pause'}
                                              >
                                                {document.getElementById(`audio-${audio.id}`)?.paused ? '▶' : '❚❚'}
                                              </button>
                                              <audio
                                                id={`audio-${audio.id}`}
                                                src={getAudioUrl(audio.fileUrl)}
                                                preload="none"
                                              />
                                            </div>
                                          ))}
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <div className={styles.noResources}>No resources available for this topic.</div>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}

              {Object.keys(groupedTopics).length === 0 && (
                <div className={styles.noSubjects}>
                  <p>No subjects available for this course.</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CourseDetailPage;
