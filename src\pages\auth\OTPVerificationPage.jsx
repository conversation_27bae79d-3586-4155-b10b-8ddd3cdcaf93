import { useEffect, useState } from 'react';
import OtpInput from 'react-otp-input';
import { useLocation, useNavigate } from 'react-router-dom';
import AnimationBox from '../../components/common/AnimationBox';
import Header from '../../components/layout/Header';
import { initializeServices } from '../../services/index.js';
import '../../styles/LoginPage.css';
import '../../styles/OTPVerification.css';

const OTPVerificationPage = () => {
  const [otp, setOtp] = useState('');
  const [error, setError] = useState('');
  const [timer, setTimer] = useState(120); // 2 minutes in seconds
  const [canResend, setCanResend] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [authService, setAuthService] = useState(null);

  const navigate = useNavigate();
  const location = useLocation();
  const email = location.state?.email || '';
  const verificationToken = location.state?.verificationToken || '';
  const purpose = location.state?.purpose || 'sign_up';

  // Configuration based on purpose
  const config = {
    sign_up: {
      
      backRoute: '/signup',
      backText: 'Wrong email?',
      backLinkText: 'Go back'
    },
    forgot_password: {
      
      backRoute: '/forgot-password',
      backText: 'Wrong email?',
      backLinkText: 'Go back'
    }
  };

  const currentConfig = config[purpose] || config.sign_up;

  // Initialize services
  useEffect(() => {
    const initServices = async () => {
      try {
        const services = await initializeServices();
        setAuthService(services.authService);
      } catch (error) {
        console.error('Failed to initialize services:', error);
        setError('Failed to initialize application. Please refresh the page.');
      }
    };

    initServices();
  }, []);

  useEffect(() => {
    if (!email) {
      navigate(currentConfig.backRoute);
      return;
    }

    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [email, navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP.');
      return;
    }

    if (!authService) {
      setError('Service not initialized. Please refresh the page.');
      return;
    }

    setIsLoading(true);

    try {
      // Debug logging
      console.log('🔍 OTP Validation Debug Info:', {
        email,
        otp,
        verificationToken,
        purpose,
        tokenLength: verificationToken?.length || 0
      });

      // Call the validateOTP API with the current purpose
      const result = await authService.validateOTP(
        email,
        otp,
        verificationToken,
        purpose
      );

      console.log('📋 OTP Validation Result:', result);

      if (result.success) {
        // Debug the OTP validation response structure
        console.log('📋 OTP Validation Full Response:', result);
        console.log('📋 OTP Validation - Data Object:', result.data);
        console.log('📋 OTP Validation - Nested Data:', result.data?.data);

        // Get the token from OTP validation response - try both possible locations
        const newToken = result.data?.data?.token || result.data?.token || verificationToken;

        console.log('🎯 Navigating to password creation with token:', {
          email,
          originalToken: verificationToken ? `${verificationToken.substring(0, 10)}...` : 'No original token',
          newToken: newToken ? `${newToken.substring(0, 10)}...` : 'No new token',
          tokenFromDataData: result.data?.data?.token ? 'Found in data.data.token' : 'Not in data.data.token',
          tokenFromData: result.data?.token ? 'Found in data.token' : 'Not in data.token',
          usingToken: newToken !== verificationToken ? 'New token from OTP response' : 'Original token from email verification'
        });

        // Navigate to next screen on success
        navigate('/set-password', {
          state: {
            email,
            // Pass the token from OTP validation response (this is what the password API needs)
            verificationToken: newToken,
            purpose
          }
        });
      } else if (result.error) {
        setError(result.error);
      }
    } catch (error) {
      console.error('OTP validation error:', error);
      setError(error.response?.data?.message || error.response?.data?.error || error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (canResend && authService) {
      setIsLoading(true);
      setError('');

      try {
        // Call the resend email verification API with the current purpose
        const result = await authService.resendEmailVerification(email, purpose);

        if (result.success) {
          // Reset timer and UI state
          setTimer(120); // 2 minutes
          setCanResend(false);
          setOtp('');
          setError('');

          // Start countdown timer
          const interval = setInterval(() => {
            setTimer((prev) => {
              if (prev <= 1) {
                setCanResend(true);
                clearInterval(interval);
                return 0;
              }
              return prev - 1;
            });
          }, 1000);

          // Show success message briefly
          setError(''); // Clear any previous errors
          // You could add a success state here if needed
        } else if (result.error) {
          setError(result.error);
        }
      } catch (error) {
        console.error('Resend OTP error:', error);
        setError(error.response?.data?.message || error.response?.data?.error || error.message);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="login-container">
      <Header />
      <AnimationBox className="login-box">
        <h4 className='h4'>Verify Account</h4>

        <div className="otp-description">
          <p className="body2">Enter OTP sent to email</p>
          {/* <p className="body3-bold email-display">{email}</p> */}
        </div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <div className="otp-container">
              <OtpInput
                value={otp}
                onChange={setOtp}
                numInputs={6}
                renderSeparator={<span className="body4 otp-separator"></span>}
                renderInput={(inputProps, idx) => (
                  <input {...inputProps} className="body4 otp-input" key={idx} />
                )}
                inputType="number"
                shouldAutoFocus={true}
              />
            </div>

            <div className="otp-timer-section">
              <span className="otp-timer">{formatTime(timer)}</span>
              <button
                type="button"
                className={`body4-bold resend-link ${!canResend ? 'disabled' : ''}`}
                onClick={handleResendOTP}
                disabled={!canResend || isLoading}
              >
                Resend OTP?
              </button>
            </div>

            {error && <div className="error-message">{error}</div>}
          </div>

          <button
            type="submit"
            className="body3-bold otp-next-button"
            disabled={otp.length !== 6 || isLoading}
          >
            {isLoading ? 'Verifying...' : 'Next'}
          </button>
        </form>

        <div className="resend-section">
          {!canResend ? (
            <p className="body4">Resend code in {formatTime(timer)}</p>
          ) : (
            <button
              className="body3-bold resend-button"
              onClick={handleResendOTP}
              disabled={isLoading}
            >
              {isLoading ? 'Resending...' : 'Resend Code'}
            </button>
          )}
        </div>

        <hr className="hr" />
        <div className="signup">
          <span className='body2'>{currentConfig.backText}</span>
          <button
            className='body3-bold'
            onClick={() => navigate(currentConfig.backRoute)}
          >
            {currentConfig.backLinkText}
          </button>
        </div>
      </AnimationBox>
    </div>
  );
};

export default OTPVerificationPage;