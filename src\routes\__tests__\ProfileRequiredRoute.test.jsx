import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ProfileRequiredRoute from '../ProfileRequiredRoute.jsx';
import { useAuthStore } from '../../stores/authStore.js';

// Mock the auth store
vi.mock('../../stores/authStore.js', () => ({
  useAuthStore: vi.fn(),
}));

// Mock Navigate component
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    Navigate: vi.fn(({ to, replace }) => (
      <div data-testid="navigate" data-to={to} data-replace={replace}>
        Navigate to {to}
      </div>
    )),
  };
});

const TestComponent = () => <div data-testid="protected-content">Protected Content</div>;

const renderWithRouter = (component, initialPath = '/') => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('ProfileRequiredRoute', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render children when user is authenticated and profile is complete', () => {
    useAuthStore.mockReturnValue({
      isAuthenticated: true,
      currentUser: { is_profile_set: true }
    });

    renderWithRouter(
      <ProfileRequiredRoute>
        <TestComponent />
      </ProfileRequiredRoute>
    );

    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
  });

  it('should redirect to login when user is not authenticated', () => {
    useAuthStore.mockReturnValue({
      isAuthenticated: false,
      currentUser: null
    });

    renderWithRouter(
      <ProfileRequiredRoute>
        <TestComponent />
      </ProfileRequiredRoute>
    );

    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toHaveAttribute('data-to', '/login');
    expect(screen.getByTestId('navigate')).toHaveAttribute('data-replace', 'true');
  });

  it('should redirect to user-details when user is authenticated but profile is incomplete', () => {
    useAuthStore.mockReturnValue({
      isAuthenticated: true,
      currentUser: { is_profile_set: false }
    });

    renderWithRouter(
      <ProfileRequiredRoute>
        <TestComponent />
      </ProfileRequiredRoute>
    );

    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toHaveAttribute('data-to', '/user-details');
    expect(screen.getByTestId('navigate')).toHaveAttribute('data-replace', 'true');
  });

  it('should redirect to user-details when user is authenticated but profile status is undefined', () => {
    useAuthStore.mockReturnValue({
      isAuthenticated: true,
      currentUser: { is_profile_set: undefined }
    });

    renderWithRouter(
      <ProfileRequiredRoute>
        <TestComponent />
      </ProfileRequiredRoute>
    );

    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toHaveAttribute('data-to', '/user-details');
  });

  it('should render children when on user-details page even if profile is incomplete', () => {
    useAuthStore.mockReturnValue({
      isAuthenticated: true,
      currentUser: { is_profile_set: false }
    });

    // Mock useLocation to return user-details path
    vi.doMock('react-router-dom', async () => {
      const actual = await vi.importActual('react-router-dom');
      return {
        ...actual,
        useLocation: () => ({ pathname: '/user-details' }),
        Navigate: vi.fn(({ to, replace }) => (
          <div data-testid="navigate" data-to={to} data-replace={replace}>
            Navigate to {to}
          </div>
        )),
      };
    });

    renderWithRouter(
      <ProfileRequiredRoute>
        <TestComponent />
      </ProfileRequiredRoute>
    );

    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
  });

  it('should handle null currentUser', () => {
    useAuthStore.mockReturnValue({
      isAuthenticated: true,
      currentUser: null
    });

    renderWithRouter(
      <ProfileRequiredRoute>
        <TestComponent />
      </ProfileRequiredRoute>
    );

    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toHaveAttribute('data-to', '/user-details');
  });

  it('should handle multiple children', () => {
    useAuthStore.mockReturnValue({
      isAuthenticated: true,
      currentUser: { is_profile_set: true }
    });

    renderWithRouter(
      <ProfileRequiredRoute>
        <div data-testid="child-1">Child 1</div>
        <div data-testid="child-2">Child 2</div>
      </ProfileRequiredRoute>
    );

    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
    expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
  });
});
