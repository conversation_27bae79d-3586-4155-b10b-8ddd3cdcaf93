import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  isTokenValid,
  validateAuthState,
  clearAuthStorage,
  validateUserDetailsAccess,
  monitorAuthState
} from '../authValidation.js';

// Mock localStorage and sessionStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  key: vi.fn(),
  length: 0,
  keys: vi.fn(() => [])
};

const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  key: vi.fn(),
  length: 0,
  keys: vi.fn(() => [])
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
  writable: true
});

Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage,
  writable: true
});

describe('authValidation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.keys.mockReturnValue([]);
    mockSessionStorage.keys.mockReturnValue([]);
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('isTokenValid', () => {
    it('should return false for null or undefined token', () => {
      expect(isTokenValid(null)).toBe(false);
      expect(isTokenValid(undefined)).toBe(false);
      expect(isTokenValid('')).toBe(false);
    });

    it('should return false for invalid token format', () => {
      expect(isTokenValid('invalid-token')).toBe(false);
      expect(isTokenValid('header.payload')).toBe(false);
      expect(isTokenValid('header.payload.signature.extra')).toBe(false);
    });

    it('should return true for valid token without expiration', () => {
      // Create a token without exp claim
      const payload = { userId: '123', email: '<EMAIL>' };
      const encodedPayload = btoa(JSON.stringify(payload));
      const token = `header.${encodedPayload}.signature`;
      
      expect(isTokenValid(token)).toBe(true);
    });

    it('should return true for non-expired token', () => {
      // Create a token that expires in the future
      const futureTime = Math.floor(Date.now() / 1000) + 3600; // 1 hour from now
      const payload = { userId: '123', exp: futureTime };
      const encodedPayload = btoa(JSON.stringify(payload));
      const token = `header.${encodedPayload}.signature`;
      
      expect(isTokenValid(token)).toBe(true);
    });

    it('should return false for expired token', () => {
      // Create a token that expired in the past
      const pastTime = Math.floor(Date.now() / 1000) - 3600; // 1 hour ago
      const payload = { userId: '123', exp: pastTime };
      const encodedPayload = btoa(JSON.stringify(payload));
      const token = `header.${encodedPayload}.signature`;
      
      expect(isTokenValid(token)).toBe(false);
    });

    it('should handle malformed payload gracefully', () => {
      const token = 'header.invalid-base64.signature';
      expect(isTokenValid(token)).toBe(false);
    });
  });

  describe('validateAuthState', () => {
    it('should return invalid for unauthenticated state', () => {
      const authState = { isAuthenticated: false, currentUser: null };
      const result = validateAuthState(authState);
      
      expect(result.isValid).toBe(false);
      expect(result.reason).toBe('Not authenticated');
      expect(result.isAuthenticated).toBe(false);
    });

    it('should return invalid when no user data', () => {
      const authState = { isAuthenticated: true, currentUser: null };
      const result = validateAuthState(authState);
      
      expect(result.isValid).toBe(false);
      expect(result.reason).toBe('No user data');
      expect(result.hasUser).toBe(false);
    });

    it('should return invalid when no token', () => {
      const authState = { 
        isAuthenticated: true, 
        currentUser: { id: '123', email: '<EMAIL>' }
      };
      const result = validateAuthState(authState);
      
      expect(result.isValid).toBe(false);
      expect(result.reason).toBe('No authentication token');
      expect(result.hasToken).toBe(false);
    });

    it('should return invalid for expired token', () => {
      const pastTime = Math.floor(Date.now() / 1000) - 3600;
      const payload = { userId: '123', exp: pastTime };
      const encodedPayload = btoa(JSON.stringify(payload));
      const expiredToken = `header.${encodedPayload}.signature`;
      
      const authState = { 
        isAuthenticated: true, 
        currentUser: { id: '123', email: '<EMAIL>', token: expiredToken }
      };
      const result = validateAuthState(authState);
      
      expect(result.isValid).toBe(false);
      expect(result.reason).toBe('Invalid or expired token');
      expect(result.isTokenValid).toBe(false);
    });

    it('should return valid for complete valid auth state', () => {
      const futureTime = Math.floor(Date.now() / 1000) + 3600;
      const payload = { userId: '123', exp: futureTime };
      const encodedPayload = btoa(JSON.stringify(payload));
      const validToken = `header.${encodedPayload}.signature`;
      
      const authState = { 
        isAuthenticated: true, 
        currentUser: { id: '123', email: '<EMAIL>', token: validToken }
      };
      const result = validateAuthState(authState);
      
      expect(result.isValid).toBe(true);
      expect(result.reason).toBe('Valid authentication state');
      expect(result.isAuthenticated).toBe(true);
      expect(result.hasUser).toBe(true);
      expect(result.hasToken).toBe(true);
      expect(result.isTokenValid).toBe(true);
    });
  });

  describe('validateUserDetailsAccess', () => {
    it('should redirect to login for invalid auth', () => {
      const authState = { isAuthenticated: false, currentUser: null };
      const result = validateUserDetailsAccess(authState);
      
      expect(result.hasAccess).toBe(false);
      expect(result.shouldRedirectToLogin).toBe(true);
      expect(result.shouldRedirectToDashboard).toBe(false);
    });

    it('should redirect to dashboard for complete profile', () => {
      const futureTime = Math.floor(Date.now() / 1000) + 3600;
      const payload = { userId: '123', exp: futureTime };
      const encodedPayload = btoa(JSON.stringify(payload));
      const validToken = `header.${encodedPayload}.signature`;
      
      const authState = { 
        isAuthenticated: true, 
        currentUser: { 
          id: '123', 
          email: '<EMAIL>', 
          token: validToken,
          is_profile_set: true
        }
      };
      const result = validateUserDetailsAccess(authState);
      
      expect(result.hasAccess).toBe(false);
      expect(result.shouldRedirectToLogin).toBe(false);
      expect(result.shouldRedirectToDashboard).toBe(true);
    });

    it('should allow access for incomplete profile', () => {
      const futureTime = Math.floor(Date.now() / 1000) + 3600;
      const payload = { userId: '123', exp: futureTime };
      const encodedPayload = btoa(JSON.stringify(payload));
      const validToken = `header.${encodedPayload}.signature`;
      
      const authState = { 
        isAuthenticated: true, 
        currentUser: { 
          id: '123', 
          email: '<EMAIL>', 
          token: validToken,
          is_profile_set: false
        }
      };
      const result = validateUserDetailsAccess(authState);
      
      expect(result.hasAccess).toBe(true);
      expect(result.shouldRedirectToLogin).toBe(false);
      expect(result.shouldRedirectToDashboard).toBe(false);
    });
  });

  describe('clearAuthStorage', () => {
    it('should clear auth-related localStorage items', () => {
      Object.defineProperty(mockLocalStorage, 'keys', {
        value: vi.fn(() => ['auth-storage', 'user-data', 'token-info', 'other-data']),
        writable: true
      });

      clearAuthStorage(true);

      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('auth-storage');
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user-data');
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('token-info');
      expect(mockLocalStorage.removeItem).not.toHaveBeenCalledWith('other-data');
    });

    it('should clear auth-related sessionStorage items', () => {
      Object.defineProperty(mockSessionStorage, 'keys', {
        value: vi.fn(() => ['auth-temp', 'user-session', 'normal-data']),
        writable: true
      });

      clearAuthStorage(true);

      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('auth-temp');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('user-session');
      expect(mockSessionStorage.removeItem).not.toHaveBeenCalledWith('normal-data');
    });
  });

  describe('monitorAuthState', () => {
    it('should call callback when auth state changes', (done) => {
      const mockCallback = vi.fn();
      
      // Mock initial empty storage
      mockLocalStorage.getItem.mockReturnValue(null);
      
      const cleanup = monitorAuthState(mockCallback);
      
      // Simulate storage change
      setTimeout(() => {
        const authData = {
          state: {
            isAuthenticated: true,
            currentUser: { id: '123', email: '<EMAIL>' }
          }
        };
        mockLocalStorage.getItem.mockReturnValue(JSON.stringify(authData));
        
        // Trigger another check
        setTimeout(() => {
          expect(mockCallback).toHaveBeenCalled();
          cleanup();
          done();
        }, 100);
      }, 100);
    });

    it('should return cleanup function', () => {
      const cleanup = monitorAuthState(vi.fn());
      expect(typeof cleanup).toBe('function');
      cleanup();
    });
  });
});
