/**
 * Device Helper Utilities - Device identification and management
 */
import { DEVICE_ID_PREFIX } from './constants.js';

/**
 * Generate a unique device ID with timestamp
 * @returns {string} Device ID in format: SEA-{timestamp}
 */
export function generateDeviceIdWithTimestamp() {
  const timestamp = new Date().getTime();
  return `${DEVICE_ID_PREFIX}-${timestamp}`;
}

/**
 * Generate a device ID with custom prefix (for testing or special cases)
 * @param {string} prefix - Custom prefix to use
 * @returns {string} Device ID with custom prefix
 */
export function generateDeviceIdWithPrefix(prefix) {
  const timestamp = new Date().getTime();
  return `${prefix}-${timestamp}`;
}

/**
 * Validate device ID format
 * @param {string} deviceId - Device ID to validate
 * @returns {boolean} True if device ID has valid format
 */
export function isValidDeviceId(deviceId) {
  if (!deviceId || typeof deviceId !== 'string') {
    return false;
  }
  
  // Check if it matches the expected format: PREFIX-TIMESTAMP
  const pattern = /^[A-Z]+\-\d+$/;
  return pattern.test(deviceId);
}

/**
 * Extract timestamp from device ID
 * @param {string} deviceId - Device ID to parse
 * @returns {number|null} Timestamp or null if invalid
 */
export function extractTimestampFromDeviceId(deviceId) {
  if (!isValidDeviceId(deviceId)) {
    return null;
  }
  
  const parts = deviceId.split('-');
  if (parts.length !== 2) {
    return null;
  }
  
  const timestamp = parseInt(parts[1], 10);
  return isNaN(timestamp) ? null : timestamp;
}

/**
 * Get device creation date from device ID
 * @param {string} deviceId - Device ID to parse
 * @returns {Date|null} Date object or null if invalid
 */
export function getDeviceCreationDate(deviceId) {
  const timestamp = extractTimestampFromDeviceId(deviceId);
  return timestamp ? new Date(timestamp) : null;
}
