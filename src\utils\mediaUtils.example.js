/**
 * Media Utils Usage Examples
 * 
 * This file demonstrates how to use the media utilities for handling
 * PDF and Audio content with AWS S3 base URLs.
 */

import {
  getPdfUrl,
  getAudioUrl,
  validateMediaUrl,
  getMediaUrlWithFallback,
  extractFilenameFromUrl,
  hasValidMediaExtension,
  getMediaTypeFromFilename,
  preloadMediaUrl
} from './mediaUtils.js';

// Example 1: Basic URL construction
console.log('=== Basic URL Construction ===');

// PDF URLs
const pdfFilename = 'documents/lesson1.pdf';
const fullPdfUrl = getPdfUrl(pdfFilename);
console.log('PDF URL:', fullPdfUrl);
// Output: https://somayyaacademy.s3.ap-south-amazonaws.com/somayyaAcademyEdTech/Pdf/documents/lesson1.pdf

// Audio URLs
const audioFilename = 'audio/lesson1.mp3';
const fullAudioUrl = getAudioUrl(audioFilename);
console.log('Audio URL:', fullAudioUrl);
// Output: https://somayyaacademy.s3.ap-south-amazonaws.com/somayyaAcademyEdTech/Audio/audio/lesson1.mp3

// Example 2: Handling different input formats
console.log('\n=== Handling Different Input Formats ===');

// With leading slashes
console.log('With leading slash:', getPdfUrl('/folder/document.pdf'));

// Already absolute URLs (returned unchanged)
console.log('Absolute URL:', getPdfUrl('https://example.com/document.pdf'));

// Protocol-relative URLs (returned unchanged)
console.log('Protocol-relative:', getPdfUrl('//cdn.example.com/document.pdf'));

// Example 3: Validation and error handling
console.log('\n=== Validation and Error Handling ===');

// Check if media files have valid extensions
const files = ['document.pdf', 'audio.mp3', 'image.jpg', 'text.txt'];
files.forEach(file => {
  const mediaType = getMediaTypeFromFilename(file);
  const isValidPdf = hasValidMediaExtension(file, 'pdf');
  const isValidAudio = hasValidMediaExtension(file, 'audio');
  
  console.log(`${file}: type=${mediaType}, validPdf=${isValidPdf}, validAudio=${isValidAudio}`);
});

// Example 4: URL validation (async)
async function validateUrls() {
  console.log('\n=== URL Validation ===');
  
  const urls = [
    getPdfUrl('existing-document.pdf'),
    getAudioUrl('existing-audio.mp3'),
    'https://nonexistent.example.com/file.pdf'
  ];
  
  for (const url of urls) {
    const isValid = await validateMediaUrl(url);
    console.log(`${url}: ${isValid ? 'accessible' : 'not accessible'}`);
  }
}

// Example 5: Fallback URL handling
async function handleFallbacks() {
  console.log('\n=== Fallback URL Handling ===');
  
  const primaryFile = 'documents/lesson1.pdf';
  const fallbackUrls = [
    'https://backup-cdn.example.com/lesson1.pdf',
    'https://mirror.example.com/lesson1.pdf'
  ];
  
  const workingUrl = await getMediaUrlWithFallback(primaryFile, 'pdf', fallbackUrls);
  console.log('Working URL:', workingUrl);
}

// Example 6: Preloading media for better performance
async function preloadMedia() {
  console.log('\n=== Media Preloading ===');
  
  const pdfUrl = getPdfUrl('documents/lesson1.pdf');
  const audioUrl = getAudioUrl('audio/lesson1.mp3');
  
  const pdfPreloaded = await preloadMediaUrl(pdfUrl, 'pdf');
  const audioPreloaded = await preloadMediaUrl(audioUrl, 'audio');
  
  console.log(`PDF preloaded: ${pdfPreloaded}`);
  console.log(`Audio preloaded: ${audioPreloaded}`);
}

// Example 7: Extracting filenames from URLs
console.log('\n=== Filename Extraction ===');

const urls = [
  'https://somayyaacademy.s3.ap-south-amazonaws.com/somayyaAcademyEdTech/Pdf/lesson1.pdf',
  'https://example.com/folder/subfolder/audio.mp3',
  'https://cdn.example.com/files/'
];

urls.forEach(url => {
  const filename = extractFilenameFromUrl(url);
  console.log(`${url} -> ${filename}`);
});

// Example 8: React component usage
export const MediaComponentExample = ({ pdfFile, audioFile }) => {
  const [pdfUrl, setPdfUrl] = React.useState('');
  const [audioUrl, setAudioUrl] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(true);
  
  React.useEffect(() => {
    const loadMedia = async () => {
      setIsLoading(true);
      
      // Convert to full URLs
      const fullPdfUrl = getPdfUrl(pdfFile);
      const fullAudioUrl = getAudioUrl(audioFile);
      
      // Validate URLs before using them
      const pdfValid = await validateMediaUrl(fullPdfUrl);
      const audioValid = await validateMediaUrl(fullAudioUrl);
      
      if (pdfValid) setPdfUrl(fullPdfUrl);
      if (audioValid) setAudioUrl(fullAudioUrl);
      
      setIsLoading(false);
    };
    
    if (pdfFile || audioFile) {
      loadMedia();
    }
  }, [pdfFile, audioFile]);
  
  if (isLoading) {
    return <div>Loading media...</div>;
  }
  
  return (
    <div>
      {pdfUrl && (
        <iframe 
          src={pdfUrl} 
          title="PDF Viewer"
          width="100%" 
          height="600px"
        />
      )}
      {audioUrl && (
        <audio controls>
          <source src={audioUrl} type="audio/mpeg" />
          Your browser does not support the audio element.
        </audio>
      )}
    </div>
  );
};

// Example 9: Error handling in components
export const SafeMediaComponent = ({ mediaFile, mediaType }) => {
  const [mediaUrl, setMediaUrl] = React.useState('');
  const [error, setError] = React.useState('');
  
  React.useEffect(() => {
    const loadMedia = async () => {
      try {
        if (!mediaFile) {
          setError('No media file provided');
          return;
        }
        
        // Validate file extension
        if (!hasValidMediaExtension(mediaFile, mediaType)) {
          setError(`Invalid ${mediaType} file extension`);
          return;
        }
        
        // Get appropriate URL
        const getUrlFunction = mediaType === 'pdf' ? getPdfUrl : getAudioUrl;
        const fullUrl = getUrlFunction(mediaFile);
        
        // Validate URL accessibility
        const isAccessible = await validateMediaUrl(fullUrl);
        if (!isAccessible) {
          setError('Media file is not accessible');
          return;
        }
        
        setMediaUrl(fullUrl);
        setError('');
        
      } catch (err) {
        setError(`Failed to load media: ${err.message}`);
      }
    };
    
    loadMedia();
  }, [mediaFile, mediaType]);
  
  if (error) {
    return <div className="error">Error: {error}</div>;
  }
  
  if (!mediaUrl) {
    return <div>Loading...</div>;
  }
  
  return (
    <div>
      <p>Media loaded successfully:</p>
      <a href={mediaUrl} target="_blank" rel="noopener noreferrer">
        Open {mediaType.toUpperCase()}
      </a>
    </div>
  );
};

// Run examples (uncomment to test)
// validateUrls();
// handleFallbacks();
// preloadMedia();
