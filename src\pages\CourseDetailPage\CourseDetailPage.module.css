.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 100vh;
  background-color: #ffffff;
}

/* Header Section */
.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.titleSection {
  flex: 1;
}

.courseTitle {
  font-size: 2.5rem;
  color: #1e3a8a;
  margin: 0;
  font-weight: 600;
  line-height: 1.2;
}

.underline {
  width: 60px;
  height: 4px;
  background-color: #ef4444;
  margin-top: 0.5rem;
}

.backButton {
  background-color: #1e3a8a;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 2rem;
}

.backButton:hover {
  background-color: #1e40af;
  transform: translateY(-1px);
}

/* Tab Navigation */
.tabNavigation {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.tab {
  background: none;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.tab:hover {
  color: #374151;
}

.activeTab {
  color: #ffffff !important;
  background-color: #1e3a8a;
  border-bottom-color: #1e3a8a;
}

/* Tab Content */
.tabContent {
  min-height: 400px;
}

/* Description Content */
.descriptionContent {
  padding: 1rem 0;
}

.sectionTitle {
  font-size: 1.8rem;
  color: #1e3a8a;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.sectionTitle::after {
  content: '';
  display: block;
  width: 40px;
  height: 3px;
  background-color: #ef4444;
  margin-top: 0.5rem;
}

.descriptionText {
  font-size: 1rem;
  line-height: 1.8;
  color: #374151;
}

.descriptionText p {
  margin-bottom: 0.75rem;
}

/* Curriculum Content */
.curriculumContent {
  padding: 1rem 0;
}

.subjectsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.subjectCard {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.subjectCard:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.subjectHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 1.5rem;
  background-color: #f9fafb;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #e5e7eb;
}

.subjectHeader:hover {
  background-color: #f3f4f6;
}

.subjectInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.expandIcon {
  font-size: 0.8rem;
  color: #6b7280;
  transition: transform 0.2s ease;
}

.expandIcon.expanded {
  transform: rotate(90deg);
}

.folderIcon {
  font-size: 1.25rem;
}

.subjectName {
  font-size: 1.1rem;
  font-weight: 500;
  color: #374151;
}

.chaptersContainer {
  background-color: #ffffff;
  padding: 0;
}

.chapterItem {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.chapterItem:last-child {
  border-bottom: none;
}

.chapterItem:hover {
  background-color: #f9fafb;
}

.chapterNumber {
  color: #6b7280;
  font-size: 0.9rem;
  margin-right: 1rem;
  min-width: 100px;
}

.chapterTitle {
  color: #374151;
  font-size: 1rem;
  font-weight: 400;
}

/* Loading and Error States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
  text-align: center;
}

.errorText {
  color: #dc2626;
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.noSubjects {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
  }

  .courseTitle {
    font-size: 2rem;
  }

  .backButton {
    margin-left: 0;
    align-self: flex-end;
  }

  .tabNavigation {
    flex-direction: column;
  }

  .tab {
    padding: 0.75rem 1rem;
    text-align: left;
  }
}
