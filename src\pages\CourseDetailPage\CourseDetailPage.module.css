.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: var(--font-family-default);
  min-height: 100vh;
  background-color: var(--bg-color);
}

/* Header Section */
.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.courseInfo {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  flex: 1;
}

.courseImageContainer {
  flex-shrink: 0;
}

.courseImage {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.coursePlaceholder {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.placeholderText {
  font-size: 2rem;
  opacity: 0.6;
}

.titleSection {
  flex: 1;
}

.courseMetadata {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.coursePrice {
  color: var(--text-color);
  font-weight: var(--font-weight-bold);
  font-size: var(--body1-size);
}

.originalPrice {
  color: var(--secondary-text-color);
  text-decoration: line-through;
  font-weight: var(--font-weight-regular);
  margin-left: 0.5rem;
}

.courseTags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.courseTag {
  background-color: #f3f4f6;
  color: var(--secondary-text-color);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: var(--body4-size);
  font-weight: 500;
}

.courseTitle {
  font-size: var(--h2-size);
  line-height: var(--h2-line-height);
  color: var(--text-color);
  margin: 0;
  font-weight: var(--font-weight-bold);
}

.underline {
  width: 60px;
  height: 4px;
  background-color: #ef4444;
  margin-top: 0.5rem;
}

.backButton {
  background-color: var(--text-color);
  color: var(--bg-color);
  border: none;
  padding: 0.75rem 2rem;
  font-size: var(--body2-size);
  font-weight: var(--font-weight-bold);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 2rem;
}

.backButton:hover {
  background-color: var(--secondary-text-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Tab Navigation */
.tabNavigation {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.tab {
  background: none;
  border: none;
  padding: 1rem 2rem;
  font-size: var(--body1-size);
  font-weight: var(--font-weight-regular);
  color: var(--secondary-text-color);
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
  font-family: var(--font-family-default);
}

.tab:hover {
  color: var(--text-color);
}

.activeTab {
  color: var(--bg-color) !important;
  background-color: var(--text-color);
  border-bottom-color: var(--text-color);
}

/* Tab Content */
.tabContent {
  min-height: 400px;
}

/* Description Content */
.descriptionContent {
  padding: 1rem 0;
}

.sectionTitle {
  font-size: var(--h3-size);
  line-height: var(--h3-line-height);
  color: var(--text-color);
  margin-bottom: 1.5rem;
  font-weight: var(--font-weight-bold);
}

.sectionTitle::after {
  content: '';
  display: block;
  width: 40px;
  height: 3px;
  background-color: #ef4444;
  margin-top: 0.5rem;
}

.descriptionText {
  font-size: var(--body2-size);
  line-height: var(--body2-line-height);
  color: var(--text-color);
}

.descriptionText p {
  margin-bottom: 0.75rem;
}

/* Curriculum Content */
.curriculumContent {
  padding: 1rem 0;
}

.subjectsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.subjectCard {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.subjectCard:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.subjectHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 1.5rem;
  background-color: #f9fafb;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #e5e7eb;
}

.subjectHeader:hover {
  background-color: #f3f4f6;
}

.subjectInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.expandIcon {
  font-size: 0.8rem;
  color: #6b7280;
  transition: transform 0.2s ease;
}

.expandIcon.expanded {
  transform: rotate(90deg);
}

/* Resources Styles - matching TopicsPage */
.resourcesContainer {
  background-color: var(--bg-color);
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.loadingResources {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--secondary-text-color);
  padding: 1rem 0;
  justify-content: center;
}

.resourcesList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.resourceItem {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.resourceItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pdfContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.pdfContainer:hover {
  background-color: #f1f3f5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pdfIcon {
  font-size: 1.5rem;
  color: #e74c3c;
  flex-shrink: 0;
}

.resourceInfo {
  flex: 1;
  min-width: 0;
}

.resourceName {
  display: block;
  font-weight: 500;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: var(--body2-size);
}

.resourceMeta {
  display: block;
  font-size: var(--body4-size);
  color: var(--secondary-text-color);
  margin-top: 0.25rem;
}

.viewButton {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.4rem 0.8rem;
  font-size: var(--body3-size);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;
}

.viewButton:hover {
  background-color: #2980b9;
  transform: translateY(-1px);
}

.viewButton:active {
  transform: translateY(0);
}

.audioList {
  margin-top: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px dashed var(--border-color);
}

.audioItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding: 0.5rem 0.75rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.audioItem:hover {
  background-color: #f1f3f5;
}

.audioInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.audioIcon {
  font-size: 1.25rem;
  color: #9b59b6;
  flex-shrink: 0;
}

.audioDetails {
  flex: 1;
  min-width: 0;
}

.audioName {
  display: block;
  font-size: var(--body3-size);
  font-weight: 500;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.audioMeta {
  display: block;
  font-size: var(--body4-size);
  color: var(--secondary-text-color);
  margin-top: 0.15rem;
}

.playButton {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #2ecc71;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.playButton:hover {
  background-color: #27ae60;
  transform: scale(1.1);
}

.playButton.playing {
  background-color: #e74c3c;
  width: 32px;
  border-radius: 4px;
}

.playButton.playing:hover {
  background-color: #c0392b;
}

.noResources {
  color: var(--secondary-text-color);
  text-align: center;
  padding: 1.5rem 0;
  font-style: italic;
  font-size: var(--body2-size);
}

.folderIcon {
  font-size: 1.25rem;
}

.subjectName {
  font-size: 1.1rem;
  font-weight: 500;
  color: #374151;
}

.chaptersContainer {
  background-color: #ffffff;
  padding: 0;
}

.topicCard {
  background: var(--bg-color);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.chapterItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.chapterItem:last-child {
  border-bottom: none;
}

.chapterItem:hover {
  background-color: #f9fafb;
}

.chapterItem.expanded {
  background-color: #f3f4f6;
  border-bottom: 1px solid var(--border-color);
}

.topicInfo {
  display: flex;
  align-items: center;
  flex: 1;
}

.chapterNumber {
  color: var(--secondary-text-color);
  font-size: var(--body3-size);
  margin-right: 1rem;
  min-width: 100px;
}

.chapterTitle {
  color: var(--text-color);
  font-size: var(--body2-size);
  font-weight: 400;
}

/* Loading and Error States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
  text-align: center;
}

.errorText {
  color: #dc2626;
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.debugText {
  color: var(--secondary-text-color);
  font-size: var(--body4-size);
  margin-bottom: 1rem;
}

.noSubjects {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
  }

  .courseInfo {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .courseImage {
    width: 100px;
    height: 100px;
  }

  .courseTitle {
    font-size: 2rem;
  }

  .backButton {
    margin-left: 0;
    align-self: flex-end;
  }

  .tabNavigation {
    flex-direction: column;
  }

  .tab {
    padding: 0.75rem 1rem;
    text-align: left;
  }
}
