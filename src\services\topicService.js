/**
 * Topic Service - Handles topic-related API calls
 */
import { apiClient, API_ENDPOINTS, withErrorHandling } from '../api/index.js';

class TopicService {
  constructor() {
    this.apiClient = apiClient;
  }

  /**
   * Fetch topics for a specific course
   * @param {string} courseId - The ID of the course
   * @returns {Promise} Service response with topics data
   */
  async getTopicsByCourseId(courseId) {
    return await withErrorHandling(
      async () => {
        // Initialize the API client
        const client = await this.apiClient.initialize();
        
        // Make the API call to get topics
        const endpoint = `/topics/getAll?courseId=${courseId}`;
        const response = await client.get(endpoint);
        
        return response.data;
      },
      { operation: 'getTopicsByCourseId', courseId }
    );
  }

  /**
   * Fetch resources for a specific topic
   * @param {string} topicId - The ID of the topic
   * @returns {Promise} Service response with resources data
   */
  async getTopicResources(topicId) {
    return await withErrorHandling(
      async () => {
        // Initialize the API client
        const client = await this.apiClient.initialize();
        
        // Make the API call to get resources
        const endpoint = `/resources/topicId/${topicId}`;
        const response = await client.get(endpoint);
        
        return response.data;
      },
      { operation: 'getTopicResources', topicId }
    );
  }
}

// Create a singleton instance
export const topicService = new TopicService();

export default topicService;
