import React, { useEffect, useRef } from "react";
import { CourseStructure, MainContent, ToolsPanel } from "../components/layout";
import { useAudioStore } from "../stores";
import { useLocalStorage } from "../hooks";
import { STORAGE_KEYS, TEXT_SIZES } from "../utils/constants";

const ReadingPage = () => {
  // Read data from session storage
  const [readingData, setReadingData] = React.useState(null);

  useEffect(() => {
    const data = sessionStorage.getItem('readingPageData');
    if (data) {
      setReadingData(JSON.parse(data));
    } else {
      console.error('No reading data found in session storage');
      // Optionally redirect back or show an error message
    }
  }, []);

  // Destructure the data once it's loaded
  const { courseData, pdfData } = readingData || {};
  const audioRef = useRef(null);
  const [textSize, setTextSize] = useLocalStorage(STORAGE_KEYS.TEXT_SIZE, TEXT_SIZES.NORMAL);

  // For single PDF viewing, we create a minimal course structure
  const effectiveCourseData = React.useMemo(() => {
    if (!pdfData) return null;

    // If we have full course data with topics, use it
    if (courseData?.topics && Array.isArray(courseData.topics)) {
      return courseData;
    }

    // Otherwise, create a minimal course structure with the single PDF as a topic
    return {
      id: courseData?.id || 'single-pdf-course',
      title: pdfData.title || 'PDF Document',
      topics: [{
        id: pdfData.id,
        title: pdfData.title,
        pdfUrl: pdfData.pdfUrl,
        audioSources: pdfData.audioSources || [],
        content: {
          heading: pdfData.title
        }
      }]
    };
  }, [courseData, pdfData]);

  const [activeTopicId, setActiveTopicId] = React.useState(pdfData?.id || "");
  const [currentTopic, setCurrentTopic] = React.useState(null);

  // Update currentTopic when data is loaded
  useEffect(() => {
    if (effectiveCourseData?.topics && activeTopicId) {
      const topic = effectiveCourseData.topics.find(t => t.id === activeTopicId);
      if (topic) {
        setCurrentTopic(topic);
      }
    } else if (pdfData && !currentTopic) {
      // Fallback to pdfData if no course structure
      setCurrentTopic({
        id: pdfData.id,
        title: pdfData.title,
        pdfUrl: pdfData.pdfUrl,
        audioSources: pdfData.audioSources || [],
        content: {
          heading: pdfData.title
        }
      });
    }
  }, [effectiveCourseData, activeTopicId, pdfData, currentTopic]);

  const initialize = useAudioStore((state) => state.initialize);
  const loadPagePlaylist = useAudioStore((state) => state.loadPagePlaylist);

  // Handle first user interaction for audio
  useEffect(() => {
    let hasInteracted = false;

    const handleFirstInteraction = () => {
      if (!hasInteracted && audioRef.current) {
        hasInteracted = true;
        // Initialize audio context on first user interaction
        audioRef.current.load();
        document.removeEventListener("click", handleFirstInteraction);
        document.removeEventListener("touchstart", handleFirstInteraction);
      }
    };

    document.addEventListener("click", handleFirstInteraction);
    document.addEventListener("touchstart", handleFirstInteraction);

    return () => {
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };
  }, []);

  // Initialize audio store
  useEffect(() => {
    if (audioRef.current && currentTopic?.audioSources) {
      const cleanup = initialize(audioRef, currentTopic.audioSources);
      return cleanup;
    }
  }, [initialize, currentTopic?.audioSources]);

  // Load audio playlist for the PDF
  useEffect(() => {
    if (currentTopic?.id && currentTopic?.audioSources) {
      loadPagePlaylist(currentTopic.id, currentTopic.audioSources);
    }
  }, [loadPagePlaylist, currentTopic]);

  // Handle topic selection
  const handleTopicSelection = React.useCallback((topicId) => {
    setActiveTopicId(topicId);
    // Find the topic in effectiveCourseData
    if (effectiveCourseData?.topics) {
      const selectedTopic = effectiveCourseData.topics.find(topic => topic.id === topicId);
      if (selectedTopic) {
        setCurrentTopic(selectedTopic);
      }
    }
  }, [effectiveCourseData?.topics]);

  // Handle navigation to specific page/topic (for bookmarks and annotations)
  const handleNavigateToPage = React.useCallback((topicId, pageNumber) => {
    console.log('Navigating to:', { topicId, pageNumber });

    // Find the topic in effectiveCourseData
    if (effectiveCourseData?.topics) {
      const targetTopic = effectiveCourseData.topics.find(topic => topic.id === topicId);
      if (targetTopic) {
        setActiveTopicId(topicId);
        setCurrentTopic(targetTopic);

        // If there's a specific page number, we could potentially scroll to it
        // For now, we'll just switch to the topic
        // TODO: Implement page-specific navigation if needed
      } else {
        console.warn('Topic not found:', topicId);
      }
    }
  }, [effectiveCourseData?.topics]);

  // Show loading state while data is being loaded
  if (!readingData) {
    return (
      <div className="app-container">
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          <div>Loading PDF...</div>
        </div>
      </div>
    );
  }

  // Show error state if no PDF data is available
  if (!pdfData || !currentTopic) {
    return (
      <div className="app-container">
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          <div>No PDF data available</div>
          <button onClick={() => window.history.back()}>Go Back</button>
        </div>
      </div>
    );
  }

  return (
    <div className="app-container">
      <audio ref={audioRef} style={{ display: "none" }} />

      {/* Course Structure Panel (Left Side) */}
      <div className="course-structure-sidebar">
        <CourseStructure
          topics={effectiveCourseData?.topics || []}
          activeTopicId={activeTopicId}
          onSelectTopic={handleTopicSelection}
          courseData={effectiveCourseData}
          onNavigateToPage={handleNavigateToPage}
        />
      </div>

      {/* Main Content Area (Center) */}
      <div className="main-content-panel">
        <MainContent
          topic={currentTopic}
          textSize={textSize}
        />
      </div>

      {/* Tools Panel (Right Side) */}
      <div className="tools-panel-sidebar">
        <ToolsPanel
          textSize={textSize}
          onTextSizeChange={setTextSize}
          currentTopic={currentTopic}
        />
      </div>
    </div>
  );
};

export default ReadingPage;
